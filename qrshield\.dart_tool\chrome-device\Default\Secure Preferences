{"browser": {"show_home_button": true}, "edge": {"services": {"account_id": "00034001BE388EAB", "last_username": "<EMAIL>"}}, "edge_fundamentals_appdefaults": {"enclave_version": 101}, "ess_kv_states": {"restore_on_startup": {"closed_notification": false, "decrypt_success": true, "key": "restore_on_startup", "notification_popup_count": 0}, "startup_urls": {"closed_notification": false, "decrypt_success": true, "key": "startup_urls", "notification_popup_count": 0}, "template_url_data": {"closed_notification": false, "decrypt_success": true, "key": "template_url_data", "notification_popup_count": 0}}, "extensions": {"settings": {"ahfgeienlihckogmohjhadlkjgocpleb": {"account_extension_type": 0, "active_permissions": {"api": ["management", "system.display", "system.storage", "webstorePrivate", "system.cpu", "system.memory", "system.network"], "explicit_host": [], "manifest_permissions": [], "scriptable_host": []}, "app_launcher_ordinal": "t", "commands": {}, "content_settings": [], "creation_flags": 1, "disable_reasons": [], "events": [], "first_install_time": "*****************", "from_webstore": false, "incognito_content_settings": [], "incognito_preferences": {}, "last_update_time": "*****************", "location": 5, "manifest": {"app": {"launch": {"web_url": "https://chrome.google.com/webstore"}, "urls": ["https://chrome.google.com/webstore"]}, "description": "Des<PERSON>bra as extensões do Microsoft Edge.", "icons": {"128": "webstore_icon_128.png", "16": "webstore_icon_16.png"}, "key": "MIGfMA0GCSqGSIb3DQEBAQUAA4GNADCBiQKBgQCtl3tO0osjuzRsf6xtD2SKxPlTfuoy7AWoObysitBPvH5fE1NaAA1/2JkPWkVDhdLBWLaIBPYeXbzlHp3y4Vv/4XG+aN5qFE3z+1RU/NqkzVYHtIpVScf3DjTYtKVL66mzVGijSoAIwbFCC3LpGdaoe6Q1rSRDp76wR6jjFzsYwQIDAQAB", "name": "Web Store", "permissions": ["webstorePrivate", "management", "system.cpu", "system.display", "system.memory", "system.network", "system.storage"], "version": "0.2"}, "needs_sync": true, "page_ordinal": "n", "path": "C:\\Program Files (x86)\\Microsoft\\Edge\\Application\\140.0.3485.14\\resources\\web_store", "preferences": {}, "regular_only_preferences": {}, "was_installed_by_default": false, "was_installed_by_oem": false}, "bmgogkgklcedkeplmalbcklcbpgelpdg": {"disable_reasons": [512]}, "cjneempfhkonkkbcmnfdibgobmhbagaj": {"active_permissions": {"api": [], "explicit_host": [], "manifest_permissions": [], "scriptable_host": []}, "disable_reasons": [8192]}, "ckcbfnpodigdcbjjmhmolhkhlfbepnca": {"disable_reasons": [1]}, "dcaajljecejllikfgbhjdgeognacjkkp": {"active_permissions": {"api": [], "explicit_host": [], "manifest_permissions": [], "scriptable_host": []}, "disable_reasons": [8192]}, "dgiklkfkllikcanfonkcabmbdfmgleag": {"account_extension_type": 0, "active_permissions": {"api": [], "explicit_host": [], "manifest_permissions": [], "scriptable_host": []}, "commands": {}, "content_settings": [], "creation_flags": 1, "disable_reasons": [], "events": [], "first_install_time": "*****************", "from_webstore": false, "incognito_content_settings": [], "incognito_preferences": {}, "last_update_time": "*****************", "location": 5, "manifest": {"content_capabilities": {"include_globs": ["https://*excel.officeapps.live.com/*", "https://*onenote.officeapps.live.com/*", "https://*powerpoint.officeapps.live.com/*", "https://*word-edit.officeapps.live.com/*", "https://*excel.officeapps.live.com.mcas.ms/*", "https://*onenote.officeapps.live.com.mcas.ms/*", "https://*word-edit.officeapps.live.com.mcas.ms/*", "https://*excel.partner.officewebapps.cn/*", "https://*onenote.partner.officewebapps.cn/*", "https://*powerpoint.partner.officewebapps.cn/*", "https://*word-edit.partner.officewebapps.cn/*", "https://*excel.gov.online.office365.us/*", "https://*onenote.gov.online.office365.us/*", "https://*powerpoint.gov.online.office365.us/*", "https://*word-edit.gov.online.office365.us/*", "https://*excel.dod.online.office365.us/*", "https://*onenote.dod.online.office365.us/*", "https://*powerpoint.dod.online.office365.us/*", "https://*word-edit.dod.online.office365.us/*", "https://*visio.partner.officewebapps.cn/*", "https://*visio.gov.online.office365.us/*", "https://*visio.dod.online.office365.us/*"], "matches": ["https://*.officeapps.live.com/*", "https://*.officeapps.live.com.mcas.ms/*", "https://*.partner.officewebapps.cn/*", "https://*.gov.online.office365.us/*", "https://*.dod.online.office365.us/*", "https://*.app.whiteboard.microsoft.com/*", "https://*.whiteboard.office.com/*", "https://*.app.int.whiteboard.microsoft.com/*", "https://*.whiteboard.office365.us/*", "https://*.dev.whiteboard.microsoft.com/*"], "permissions": ["clipboardRead", "clipboardWrite"]}, "default_locale": "en", "description": "This extension grants Microsoft web sites permission to read and write from the clipboard.", "key": "MIGfMA0GCSqGSIb3DQEBAQUAA4GNADCBiQKBgQCz4t/X7GeuP6GBpjmxndrjtzF//4CWeHlC68rkoV7hP3h5Ka6eX7ZMNlYJkSjmB5iRmPHO5kR1y7rGY8JXnRPDQh/CQNLVA7OsKeV6w+UO+vx8KGI+TrTAhzH8YGcMIsxsUjxtC4cBmprja+xDr0zVp2EMgqHu+GBKgwSRHTkDuwIDAQAB", "manifest_version": 2, "minimum_chrome_version": "77", "name": "Microsoft Clipboard Extension", "version": "1.0"}, "path": "C:\\Program Files (x86)\\Microsoft\\Edge\\Application\\140.0.3485.14\\resources\\edge_clipboard", "preferences": {}, "regular_only_preferences": {}, "was_installed_by_default": false, "was_installed_by_oem": false}, "ehlmnljdoejdahfjdfobmpfancoibmig": {"active_permissions": {"api": [], "explicit_host": [], "manifest_permissions": [], "scriptable_host": []}, "disable_reasons": [8192]}, "ejefaeioamebhekmfaclajddbpnnobje": {"lastpingday": "*****************"}, "feaiphogcfmhkkbodjnmbjcbaeiikbbm": {"lastpingday": "*****************"}, "fikbjbembnmfhppjfnmfkahdhfohhjmg": {"account_extension_type": 0, "active_permissions": {"api": [], "explicit_host": [], "manifest_permissions": [], "scriptable_host": []}, "commands": {}, "content_settings": [], "creation_flags": 1, "disable_reasons": [], "events": [], "first_install_time": "*****************", "from_webstore": false, "incognito_content_settings": [], "incognito_preferences": {}, "last_update_time": "*****************", "location": 5, "manifest": {"background": {"persistent": false, "scripts": ["background.js"]}, "externally_connectable": {"matches": ["https://*.microsoftstream.com/*"]}, "incognito": "split", "key": "MIIBIjANBgkqhkiG9w0BAQEFAAOCAQ8AMIIBCgKCAQEAsAmDrYmQaYQlLxSAn/jTQTGNt1IffJGIJeKucE/B42d8QIyFD2RCarmHP1bmbY1YuTng2dL3J//qyvUNwXPt9cmxH9WKwi512tzOa5r2zYaCuOgP2vAIrah/bKnpO3XmUfFWj+LRcbZahOmMDMQxzPKxFKuIz2eOiakBXDE6Ok7azHJ13LLQTte1JgZIPmyFrAciPABLp/IKLfsfnebVW1YgaOyxBNyp/7bhSmoyZI3kBv8InKOpGE8pttrBg6l5zkvD67a7ViNAYkqZIpJJV5ZTQtVWCWSG0xU2y+3zXZtx8KbGbDiWUAcwNYDVPpsV+IQXVpgAplHvrZme+hAl6QIDAQAB", "manifest_version": 2, "name": "Media Internals Services Extension", "permissions": ["mediaInternalsPrivate"], "version": "2.0.0"}, "path": "C:\\Program Files (x86)\\Microsoft\\Edge\\Application\\140.0.3485.14\\resources\\media_internals_services", "preferences": {}, "regular_only_preferences": {}, "was_installed_by_default": false, "was_installed_by_oem": false}, "fjngpfnaikknjdhkckmncgicobbkcnle": {"active_permissions": {"api": [], "explicit_host": [], "manifest_permissions": [], "scriptable_host": []}, "disable_reasons": [8192]}, "gbihlnbpmfkodghomcinpblknjhneknc": {"active_permissions": {"api": [], "explicit_host": [], "manifest_permissions": [], "scriptable_host": []}, "disable_reasons": [8192]}, "gbmoeijgfngecijpcnbooedokgafmmji": {"active_permissions": {"api": [], "explicit_host": [], "manifest_permissions": [], "scriptable_host": []}, "disable_reasons": [8192]}, "gcinnojdebelpnodghnoicmcdmamjoch": {"active_permissions": {"api": [], "explicit_host": [], "manifest_permissions": [], "scriptable_host": []}, "disable_reasons": [8192]}, "gecfnmoodchdkebjjffmdcmeghkflpib": {"active_permissions": {"api": [], "explicit_host": [], "manifest_permissions": [], "scriptable_host": []}, "disable_reasons": [8192]}, "hfmgbegjielnmfghmoohgmplnpeehike": {"active_permissions": {"api": [], "explicit_host": [], "manifest_permissions": [], "scriptable_host": []}, "disable_reasons": [8192]}, "iglcjdemknebjbklcgkfaebgojjphkec": {"account_extension_type": 0, "active_permissions": {"api": ["identity", "management", "metricsPrivate", "webstorePrivate", "hubPrivate"], "explicit_host": [], "manifest_permissions": [], "scriptable_host": []}, "app_launcher_ordinal": "w", "commands": {}, "content_settings": [], "creation_flags": 1, "disable_reasons": [], "events": [], "first_install_time": "*****************", "from_webstore": false, "incognito_content_settings": [], "incognito_preferences": {}, "last_update_time": "*****************", "location": 5, "manifest": {"app": {"launch": {"web_url": "https://microsoftedge.microsoft.com"}, "urls": ["https://microsoftedge.microsoft.com"]}, "description": "Des<PERSON>bra as extensões do Microsoft Edge.", "icons": {"128": "webstore_icon_128.png", "16": "webstore_icon_16.png"}, "key": "MIIBIjANBgkqhkiG9w0BAQEFAAOCAQ8AMIIBCgKCAQEAtMvN4+y6cd3el/A/NT5eUnrz1WiD1WJRaJfMBvaMtJHIuFGEmYdYL/YuE74J19+pwhjOHeFZ3XUSMTdOa5moOaXXvdMr5wWaaN2frHewtAnNDO64NGbbZvdsfGm/kRkHKVGNV6dacZsAkylcz5CkwTmq97wOZ7ETaShHvhZEGwRQIt4K1poxurOkDYQw9ERZNf3fgYJ9ZTrLZMAFDLJY+uSF03pClWrr8VGc8LUQ4Naktb8QSgVUlrS14AdF/ESdbhnTvvdB0e7peNWRyoNtCqLJsbtTtBL6sOnqfusnwPowuueOFI+XskOT9TvLo6PcgxhLX5+d0mM+Jtn6PFTU8QIDAQAB", "name": "Microsoft Store", "permissions": ["webstorePrivate", "management", "metricsPrivate", "identity", "hubPrivate"], "version": "0.2"}, "needs_sync": true, "page_ordinal": "n", "path": "C:\\Program Files (x86)\\Microsoft\\Edge\\Application\\140.0.3485.14\\resources\\microsoft_web_store", "preferences": {}, "regular_only_preferences": {}, "was_installed_by_default": false, "was_installed_by_oem": false}, "ihmafllikibpmigkcoadcmckbfhibefp": {"account_extension_type": 0, "active_permissions": {"api": ["debugger", "feedbackPrivate", "fileSystem", "fileSystem.write", "app.window.fullscreen", "metricsPrivate", "storage", "tabs", "fileSystem.readFullPath", "edgeInternetConnectivityPrivate"], "explicit_host": ["edge://resources/*"], "manifest_permissions": [], "scriptable_host": []}, "commands": {}, "content_settings": [], "creation_flags": 1, "disable_reasons": [], "events": ["edgeFeedbackPrivate.onFeedbackRequested"], "first_install_time": "*****************", "from_webstore": false, "incognito_content_settings": [], "incognito_preferences": {}, "last_update_time": "*****************", "location": 5, "manifest": {"app": {"background": {"scripts": ["js/event_handler.js"]}, "content_security_policy": "default-src 'none'; script-src 'self' blob: filesystem: chrome://resources; style-src 'unsafe-inline' blob: chrome: file: filesystem: data: *; img-src * blob: chrome: file: filesystem: data:; media-src 'self' blob: filesystem:; connect-src data:"}, "description": "User feedback extension", "display_in_launcher": false, "display_in_new_tab_page": false, "icons": {"128": "images/icon128.png", "16": "images/icon16.png", "192": "images/icon192.png", "32": "images/icon32.png", "48": "images/icon48.png"}, "incognito": "split", "key": "MIIBIjANBgkqhkiG9w0BAQEFAAOCAQ8AMIIBCgKCAQEAl3vxWwvLjcMIFK4OfG6C8PmJkMhFYDKRnx+SqG23YlMG1A+bOkiNmAN1TWpFPPp1f2PpbiZGNq1y29u/QfkD+PC4bnO7GbNw/2X5tGoP0n2K+KGGAxhnr0ki/oyo2eiFGSTOXlQvTRo5q1vB+Lbg+9TbFsWKlHZyAkeZ/YGz/iijHTqw8Q4RWdl5Tp8SlUhS/92EsWhveNJLW22veaT/Up2iSeSSwfyoHVYy8LUPaD4fbyLvPQacVLJq1dac2bNDqjaNvSPgPWCnkZtDmawZrgxT53otLCES/e96xfAf8I24VHIc1pVP8LqdqKr1AV1Yxn93h3VJ2QejtEhIAWHU6QIDAQAB", "manifest_version": 2, "name": "<PERSON>", "permissions": ["chrome://resources/", "debugger", "edgeInternetConnectivityPrivate", "feedbackPrivate", {"fileSystem": ["<PERSON><PERSON><PERSON><PERSON><PERSON>", "write"]}, "fullscreen", "metricsPrivate", "storage", "windows"], "version": "*******"}, "path": "C:\\Program Files (x86)\\Microsoft\\Edge\\Application\\140.0.3485.14\\resources\\edge_feedback", "preferences": {}, "regular_only_preferences": {}, "running": false, "was_installed_by_default": false, "was_installed_by_oem": false}, "jbleckejnaboogigodiafflhkajdmpcl": {"active_permissions": {"api": [], "explicit_host": [], "manifest_permissions": [], "scriptable_host": []}, "disable_reasons": [8192]}, "jdiccldimpdaibmpdkjnbmckianbfold": {"account_extension_type": 0, "active_permissions": {"api": ["activeTab", "metricsPrivate", "storage", "systemPrivate", "ttsEngine", "errorReporting"], "explicit_host": ["https://*.bing.com/*"], "manifest_permissions": [], "scriptable_host": []}, "commands": {}, "content_settings": [], "creation_flags": 1, "disable_reasons": [], "events": ["ttsEngine.onPause", "ttsEngine.onResume", "ttsEngine.onSpeak", "ttsEngine.onStop"], "first_install_time": "*****************", "from_webstore": false, "incognito_content_settings": [], "incognito_preferences": {}, "last_update_time": "*****************", "location": 5, "manifest": {"background": {"persistent": false, "scripts": ["lifetimeHelper.js", "telemetryHelper.js", "errorHelper.js", "voiceList/voiceListRequester.js", "voiceList/voiceListSingleton.js", "voiceList/voiceModel.js", "manifestHelper.js", "config.js", "ssml.js", "uuid.js", "wordBoundary.js", "audioStreamer.js", "wordBoundaryEventManager.js", "audioViewModel.js", "background.js"]}, "description": "Provides access to Microsoft's online text-to-speech voices", "key": "AAAAB3NzaC1yc2EAAAADAQABAAAAgQDjGOAV6/3fmEtQmFqlmqm5cZ+jlNhd6XikwMDp0I7BKh+AjG3aBIG/qqwlsF/7LAGatnSxBwUwZC0qMnGXtcOPVl26Q8OvMx0gt5Va5gxca+ae0Skluj9WN9TNxPFVhw21WbCt4D9q3kb+XXDlx/7v1ktYus4Fwr/skkjADG9cIQ==", "manifest_version": 2, "name": "Microsoft Voices", "permissions": ["activeTab", "errorReporting", "metricsPrivate", "storage", "systemPrivate", "ttsEngine", "https://*.bing.com/"], "tts_engine": {"voices": [{"codec": "audio-24khz-48kbitrate-mono-mp3", "event_types": ["end", "error", "start", "word"], "lang": "en-US", "remote": true, "server_name": "Microsoft Server Speech Text to Speech Voice (en-US, AriaNeural)", "voice_name": "Microsoft Aria Online (Natural) - English (United States)"}, {"codec": "audio-24khz-48kbitrate-mono-mp3", "event_types": ["end", "error", "start", "word"], "lang": "en-US", "remote": true, "server_name": "Microsoft Server Speech Text to Speech Voice (en-US, GuyNeural)", "voice_name": "Microsoft Guy Online (Natural) - English (United States)"}, {"codec": "audio-24khz-48kbitrate-mono-mp3", "event_types": ["end", "error", "start", "word"], "lang": "zh-CN", "remote": true, "server_name": "Microsoft Server Speech Text to Speech Voice (zh<PERSON><PERSON><PERSON>, XiaoxiaoNeural)", "voice_name": "Microsoft Xiaoxiao Online (Natural) - Chinese (Mainland)"}, {"codec": "audio-24khz-48kbitrate-mono-mp3", "event_types": ["end", "error", "start", "word"], "lang": "zh-CN", "remote": true, "server_name": "Microsoft Server Speech Text to Speech Voice (zh-CN, YunyangNeural)", "voice_name": "Microsoft Yunyang Online (Natural) - Chinese (Mainland)"}, {"codec": "audio-16khz-32kbitrate-mono-mp3", "event_types": ["end", "error", "start", "word"], "lang": "zh-TW", "remote": true, "server_name": "Microsoft Server Speech Text to Speech Voice (zh-TW, HanHanRUS)", "voice_name": "Microsoft HanHan Online - Chinese (Taiwan)"}, {"codec": "audio-16khz-32kbitrate-mono-mp3", "event_types": ["end", "error", "start", "word"], "lang": "zh-HK", "remote": true, "server_name": "Microsoft Server Speech Text to Speech Voice (zh-HK, TracyRUS)", "voice_name": "Microsoft Tracy Online - Chinese (Hong Kong)"}, {"codec": "audio-24khz-48kbitrate-mono-mp3", "event_types": ["end", "error", "start", "word"], "lang": "ja-<PERSON>", "remote": true, "server_name": "Microsoft Server Speech Text to Speech Voice (<PERSON><PERSON><PERSON><PERSON>, NanamiNeural)", "voice_name": "Microsoft Nanami Online (Natural) - Japanese (Japan)"}, {"codec": "audio-24khz-48kbitrate-mono-mp3", "event_types": ["end", "error", "start", "word"], "lang": "en-GB", "remote": true, "server_name": "Microsoft Server Speech Text to Speech Voice (en-GB, LibbyNeural)", "voice_name": "Microsoft Libby Online (Natural) - English (United Kingdom)"}, {"codec": "audio-24khz-48kbitrate-mono-mp3", "event_types": ["end", "error", "start", "word"], "lang": "pt-BR", "remote": true, "server_name": "Microsoft Server Speech Text to Speech Voice (pt-BR, FranciscaNeural)", "voice_name": "Microsoft Francisca Online (Natural) - Portuguese (Brazil)"}, {"codec": "audio-24khz-48kbitrate-mono-mp3", "event_types": ["end", "error", "start", "word"], "lang": "es-MX", "remote": true, "server_name": "Microsoft Server Speech Text to Speech Voice (es-MX, DaliaNeural)", "voice_name": "Microsoft Dalia Online (Natural) - Spanish (Mexico)"}, {"codec": "audio-16khz-32kbitrate-mono-mp3", "event_types": ["end", "error", "start", "word"], "lang": "en-IN", "remote": true, "server_name": "Microsoft Server Speech Text to Speech Voice (en-IN, PriyaRUS)", "voice_name": "Microsoft Priya Online - English (India)"}, {"codec": "audio-16khz-32kbitrate-mono-mp3", "event_types": ["end", "error", "start", "word"], "lang": "en-CA", "remote": true, "server_name": "Microsoft Server Speech Text to Speech Voice (en-CA, HeatherRUS)", "voice_name": "Microsoft Heather Online - English (Canada)"}, {"codec": "audio-24khz-48kbitrate-mono-mp3", "event_types": ["end", "error", "start", "word"], "lang": "fr-CA", "remote": true, "server_name": "Microsoft Server Speech Text to Speech Voice (fr-CA, SylvieNeural)", "voice_name": "Microsoft Sylvie Online (Natural) - French (Canada)"}, {"codec": "audio-24khz-48kbitrate-mono-mp3", "event_types": ["end", "error", "start", "word"], "lang": "fr-FR", "remote": true, "server_name": "Microsoft Server Speech Text to Speech Voice (fr-<PERSON><PERSON>, <PERSON>)", "voice_name": "Microsoft Denise Online (Natural) - French (France)"}, {"codec": "audio-24khz-48kbitrate-mono-mp3", "event_types": ["end", "error", "start", "word"], "lang": "de-DE", "remote": true, "server_name": "Microsoft Server Speech Text to Speech Voice (de-DE, KatjaNeural)", "voice_name": "Microsoft Katja Online (Natural) - German (Germany)"}, {"codec": "audio-16khz-32kbitrate-mono-mp3", "event_types": ["end", "error", "start", "word"], "lang": "ru-RU", "remote": true, "server_name": "Microsoft Server Speech Text to Speech Voice (ru-RU, EkaterinaRUS)", "voice_name": "Microsoft Ekaterina Online - Russian (Russia)"}, {"codec": "audio-16khz-32kbitrate-mono-mp3", "event_types": ["end", "error", "start", "word"], "lang": "en-AU", "remote": true, "server_name": "Microsoft Server Speech Text to Speech Voice (en-AU, HayleyRUS)", "voice_name": "Microsoft Hayley Online - English (Australia)"}, {"codec": "audio-24khz-48kbitrate-mono-mp3", "event_types": ["end", "error", "start", "word"], "lang": "it-IT", "remote": true, "server_name": "Microsoft Server Speech Text to Speech Voice (it-IT, ElsaNeural)", "voice_name": "Microsoft Elsa Online (Natural) - Italian (Italy)"}, {"codec": "audio-24khz-48kbitrate-mono-mp3", "event_types": ["end", "error", "start", "word"], "lang": "ko-KR", "remote": true, "server_name": "Microsoft Server Speech Text to Speech Voice (ko-KR, SunHiNeural)", "voice_name": "Microsoft SunHi Online (Natural) - Korean (Korea)"}, {"codec": "audio-16khz-32kbitrate-mono-mp3", "event_types": ["end", "error", "start", "word"], "lang": "nl-NL", "remote": true, "server_name": "Microsoft Server Speech Text to Speech Voice (nl-NL, HannaRUS)", "voice_name": "Microsoft Hanna Online - Dutch (Netherlands)"}, {"codec": "audio-24khz-48kbitrate-mono-mp3", "event_types": ["end", "error", "start", "word"], "lang": "es-ES", "remote": true, "server_name": "Microsoft Server Speech Text to Speech Voice (es-ES, ElviraNeural)", "voice_name": "Microsoft Elvira Online (Natural) - Spanish (Spain)"}, {"codec": "audio-24khz-48kbitrate-mono-mp3", "event_types": ["end", "error", "start", "word"], "lang": "tr-TR", "remote": true, "server_name": "Microsoft Server Speech Text to Speech Voice (tr-TR, EmelNeural)", "voice_name": "Microsoft Emel Online (Natural) - Turkish (Turkey)"}, {"codec": "audio-16khz-32kbitrate-mono-mp3", "event_types": ["end", "error", "start", "word"], "lang": "pl-PL", "remote": true, "server_name": "Microsoft Server Speech Text to Speech Voice (pl-PL, PaulinaRUS)", "voice_name": "Microsoft Paulina Online - Polish (Poland)"}]}, "version": "1.0"}, "path": "C:\\Program Files (x86)\\Microsoft\\Edge\\Application\\140.0.3485.14\\resources\\microsoft_voices", "preferences": {}, "regular_only_preferences": {}, "was_installed_by_default": false, "was_installed_by_oem": false}, "kagpabjoboikccfdghpdlaaopmgpgfdc": {"lastpingday": "*****************"}, "kfihiegbjaloebkmglnjnljoljgkkchm": {"active_permissions": {"api": [], "explicit_host": [], "manifest_permissions": [], "scriptable_host": []}, "disable_reasons": [8192]}, "llhcnbijpnechllogkacbcjmkcgjbjfi": {"lastpingday": "*****************"}, "mhjfbmdgcfjbbpaeojofohoefgiehjai": {"account_extension_type": 0, "active_permissions": {"api": ["contentSettings", "fileSystem", "fileSystem.write", "metricsPrivate", "tabs", "resourcesPrivate", "pdfViewerPrivate", "fileSystem.readFullPath", "errorReporting", "edgeLearningToolsPrivate", "fileSystem.getCurrentEntry", "edgePdfPrivate", "edgeCertVerifierPrivate"], "explicit_host": ["edge://resources/*", "edge://webui-test/*"], "manifest_permissions": [], "scriptable_host": []}, "commands": {}, "content_settings": [], "creation_flags": 1, "disable_reasons": [], "events": [], "first_install_time": "*****************", "from_webstore": false, "incognito_content_settings": [], "incognito_preferences": {}, "last_update_time": "*****************", "location": 5, "manifest": {"content_security_policy": "script-src 'self' 'wasm-eval' blob: filesystem: chrome://resources chrome://webui-test; object-src * blob: externalfile: file: filesystem: data:; trusted-types edge-internal fast-html pdf-url edge-pdf-static-policy;", "description": "", "incognito": "split", "key": "MIGfMA0GCSqGSIb3DQEBAQUAA4GNADCBiQKBgQDN6hM0rsDYGbzQPQfOygqlRtQgKUXMfnSjhIBL7LnReAVBEd7ZmKtyN2qmSasMl4HZpMhVe2rPWVVwBDl6iyNE/Kok6E6v6V3vCLGsOpQAuuNVye/3QxzIldzG/jQAdWZiyXReRVapOhZtLjGfywCvlWq7Sl/e3sbc0vWybSDI2QIDAQAB", "manifest_version": 2, "mime_types": ["application/pdf"], "mime_types_handler": "edge_pdf/index.html", "name": "Microsoft Edge PDF Viewer", "offline_enabled": true, "permissions": ["errorReporting", "chrome://resources/", "chrome://webui-test/", "contentSettings", "metricsPrivate", "edgeCertVerifierPrivate", "edgeLearningToolsPrivate", "edgePdfPrivate", "pdfViewerPrivate", "resourcesPrivate", "tabs", {"fileSystem": ["write", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "getCurrentEntry"]}], "version": "1"}, "path": "C:\\Program Files (x86)\\Microsoft\\Edge\\Application\\140.0.3485.14\\resources\\edge_pdf", "preferences": {}, "regular_only_preferences": {}, "was_installed_by_default": false, "was_installed_by_oem": false}, "ncbjelpjchkpbikbpkcchkhkblodoama": {"account_extension_type": 0, "active_permissions": {"api": [], "explicit_host": [], "manifest_permissions": [], "scriptable_host": []}, "commands": {}, "content_settings": [], "creation_flags": 1, "disable_reasons": [], "events": [], "first_install_time": "*****************", "from_webstore": false, "incognito_content_settings": [], "incognito_preferences": {}, "last_update_time": "*****************", "location": 5, "manifest": {"background": {"persistent": false, "scripts": ["background.js"]}, "externally_connectable": {"matches": ["https://*.teams.microsoft.com/*", "https://*.skype.com/*", "https://*.teams.live.com/*"]}, "incognito": "split", "key": "MIIBIjANBgkqhkiG9w0BAQEFAAOCAQ8AMIIBCgKCAQEAtAdFAR3ckd5c7G8VSzUj4Ltt/QRInUOD00StG95LweksGcLBlFlYL46cHFVgHHj1gmzcpBtgsURdcrAC3V8yiE7GY4wtpOP+9l+adUGR+cyOG0mw9fLjyH+2Il0QqktsNXzkNiE1ogW4l0h4+PJc262j0vtm4hBzMvR0QScFWcAIcAErlUiWTt4jefXCAYqubV99ed5MvVMWBxe97wOa9hYwAhbCminOepA4RRTg9eyi0TiuHpq/bNI8C5qZgKIQNBAjgiFBaIx9hiMBFlK4NHUbFdgY6Qp/hSCMNurctwz1jpsXEnT4eHg1YWXfquoH8s4swIjkFCMBF6Ejc3cUkQIDAQAB", "manifest_version": 2, "name": "WebRTC Internals Extension", "permissions": ["webrtcInternalsPrivate"], "version": "2.0.2"}, "path": "C:\\Program Files (x86)\\Microsoft\\Edge\\Application\\140.0.3485.14\\resources\\webrtc_internals", "preferences": {}, "regular_only_preferences": {}, "was_installed_by_default": false, "was_installed_by_oem": false}, "ndcileolkflehcjpmjnfbnaibdcgglog": {"lastpingday": "*****************"}, "njjljiblognghfjfpcdpdbpbfcmhgafg": {"lastpingday": "*****************"}, "nkbndigcebkoaejohleckhekfmcecfja": {"active_permissions": {"api": [], "explicit_host": [], "manifest_permissions": [], "scriptable_host": []}, "disable_reasons": [8192]}, "nkeimhogjdpnpccoofpliimaahmaaome": {"account_extension_type": 0, "active_permissions": {"api": ["processes", "webrtcLoggingPrivate", "system.cpu", "enterprise.hardwarePlatform"], "explicit_host": [], "manifest_permissions": [], "scriptable_host": []}, "commands": {}, "content_settings": [], "creation_flags": 1, "disable_reasons": [], "events": ["runtime.onConnectExternal"], "first_install_time": "*****************", "from_webstore": false, "incognito_content_settings": [], "incognito_preferences": {}, "last_update_time": "*****************", "location": 5, "manifest": {"background": {"page": "background.html", "persistent": false}, "externally_connectable": {"matches": ["https://*.teams.microsoft.com/*", "https://*.teams.live.com/*", "https://*.skype.com/*"]}, "incognito": "split", "key": "MIGfMA0GCSqGSIb3DQEBAQUAA4GNADCBiQKBgQDAQt2ZDdPfoSe/JI6ID5bgLHRCnCu9T36aYczmhw/tnv6QZB2I6WnOCMZXJZlRdqWc7w9jo4BWhYS50Vb4weMfh/I0On7VcRwJUgfAxW2cHB+EkmtI1v4v/OU24OqIa1Nmv9uRVeX0GjhQukdLNhAE6ACWooaf5kqKlCeK+1GOkQIDAQAB", "manifest_version": 2, "name": "WebRTC Extension", "permissions": ["enterprise.hardwarePlatform", "processes", "system.cpu", "webrtcLoggingPrivate"], "version": "2.3.24"}, "path": "C:\\Program Files (x86)\\Microsoft\\Edge\\Application\\140.0.3485.14\\resources\\hangout_services", "preferences": {}, "regular_only_preferences": {}, "was_installed_by_default": false, "was_installed_by_oem": false}, "nlbmdekgjkajiobkcbpolefohlelfhfe": {"lastpingday": "*****************"}, "ofefcgjbeghpigppfmkologfjadafddi": {"active_permissions": {"api": [], "explicit_host": [], "manifest_permissions": [], "scriptable_host": []}, "disable_reasons": [8192]}}}, "google": {"services": {"last_signed_in_username": "<EMAIL>"}}, "homepage": "http://www.google.com.br/", "homepage_is_newtabpage": true, "protection": {"macs": {"browser": {"show_home_button": "AD488728007881E54A017AED167D4EE021A44BE39182D15A459FF742C4C2FCBB"}, "default_search_provider_data": {"template_url_data": "14EABC7531565D830D75F3BCFCECE0B53ACE524F62F5E0183C8EF549BCFFC82D"}, "edge": {"services": {"account_id": "1DFC060CCC4589D1B41C8E10E4E906FEAFBAD1ECC3F7CBCE2C5F71296F379D3E", "last_username": "4BAB708D17261B8D71EB3960885169592E80F27BB9A5FB6AF6A9FDA47C0833B4"}}, "enterprise_signin": {"policy_recovery_token": "8F32BF7D1DD530E011EE9027545EFA60A4B913A8CACC80652DCCA29B5CE8B997"}, "extensions": {"settings": {"ahfgeienlihckogmohjhadlkjgocpleb": "C6803BF1917033334E0FF65C0ADC983760D210351C01029714A3F61CED4B3B8C", "bmgogkgklcedkeplmalbcklcbpgelpdg": "7695813B68BECC2119BDE1993FD085AE9241948BEB685BAC821E596496A5007D", "cjneempfhkonkkbcmnfdibgobmhbagaj": "E1041207A246C2CCBB2BA55C471A89B19B8BE5A2E4FE301825A02C2C8B79C6D4", "ckcbfnpodigdcbjjmhmolhkhlfbepnca": "2F2776EF2D250B9B7174760935C061B09A6FB4072A4637D155439DAECD8DED34", "dcaajljecejllikfgbhjdgeognacjkkp": "A29D1E96DAFE532A030B30C68A6E53A37A08F79D2221AC41FDE19A8F97619845", "dgiklkfkllikcanfonkcabmbdfmgleag": "CB1CAE6FA8BEA1BD1496BA0F9BE1D46421B9D9CECBA1B1E0DF6BAD225A64D90E", "ehlmnljdoejdahfjdfobmpfancoibmig": "11A90EBA438A5CA5B3D87ABE1FBFCA956859E40BEF53D75DDC9E80DFB7A23D4C", "ejefaeioamebhekmfaclajddbpnnobje": "6A2BEC90D53808E0A4020A4122FBEB3910E7BBBCA22630C0DDEAB16F9DADF1C6", "feaiphogcfmhkkbodjnmbjcbaeiikbbm": "089DF46C9F278AAB9854807695FD8552CAAA54D5512A1CCC5852CDE8907C062B", "fikbjbembnmfhppjfnmfkahdhfohhjmg": "FADEFF99DB0DC4362212BC20CC6D0811BAAC3AFA793433D3B380A142EEF0E999", "fjngpfnaikknjdhkckmncgicobbkcnle": "C8660D631E4082CE3F4B7DD7E9FE691070B0B7D567500C9DA3463E745A7728E8", "gbihlnbpmfkodghomcinpblknjhneknc": "142D801C47FA4F0384A9133D45A69D3FA9EC772E1EBDF933619BD0FFCC6510DC", "gbmoeijgfngecijpcnbooedokgafmmji": "1D8B003FE5239AA302B78AD916393A9336D94309663E6E949EE3671087875772", "gcinnojdebelpnodghnoicmcdmamjoch": "C698363B88F02E9F92A860A9CAE22774DF7D8D6FE3FEA1B35C47C1F164D7ED58", "gecfnmoodchdkebjjffmdcmeghkflpib": "990D0024961006EF3221E79116A0249FFEBB61B3803978179A138AD87061686D", "hfmgbegjielnmfghmoohgmplnpeehike": "8CA7A3697354E5B723B8B9B15AA1F5F0FEF0A8D2BCBF1C222DD860274B3D274A", "iglcjdemknebjbklcgkfaebgojjphkec": "D560EA34117C897D428CBF29107894F513438BB7D3DE8F47891F520EF0E8A408", "ihmafllikibpmigkcoadcmckbfhibefp": "5ED2CAEBD845874583434E7250C5D8B76B1C521849AC7F39C05C5786B8FF3EC3", "jbleckejnaboogigodiafflhkajdmpcl": "81369E6F07CB515BEB089D5CA0398EDCDF777167431A23111F58F4BA39FBFD8A", "jdiccldimpdaibmpdkjnbmckianbfold": "8D5BC8BC3A1BE9BCFB767BABABC97965D2B0C165BC492A8F4B8F39752ADDB127", "kagpabjoboikccfdghpdlaaopmgpgfdc": "75BF6D8D482A931C89837460A1854E0C926655B4DF8F2939EA21B341B09276C6", "kfihiegbjaloebkmglnjnljoljgkkchm": "2B865CA61C477DE72B8CC1CFA3026C5002F73B95DFB46F54410E916C494447AB", "llhcnbijpnechllogkacbcjmkcgjbjfi": "FF4F38F6EBE9E81B411086F357A1F16AF5AA06F5985F362BAA942FD32AC26C09", "mhjfbmdgcfjbbpaeojofohoefgiehjai": "BFFF102C78C2E9FC01E737721D33E40EB89CE916F514E07208490F781948F13B", "ncbjelpjchkpbikbpkcchkhkblodoama": "A0BB447098DD2B320BCFB052E4189675674A413BFBC4CEF376337579BD676351", "ndcileolkflehcjpmjnfbnaibdcgglog": "CD2D4DC47E31E4E016241BBBDD7FE8DEB37869854445BC335045D46124F3FF1A", "njjljiblognghfjfpcdpdbpbfcmhgafg": "D37D9A985084E44CF553B5E60B472B3AA81077D4C95AF15289DEA4E35344F86B", "nkbndigcebkoaejohleckhekfmcecfja": "63554351013E438BBD1F69CAC43624A99F5FE31B9BF9BD0C3F7CCA7607E42A74", "nkeimhogjdpnpccoofpliimaahmaaome": "04FF85FE682D31025F729661C90C7D8315B6415F17EC82BFF2DA30052176245A", "nlbmdekgjkajiobkcbpolefohlelfhfe": "F83E9A8ADBF5A0868D73855C75B1ACC84CAE55F25D084D043B1E1B652530B4E5", "ofefcgjbeghpigppfmkologfjadafddi": "02D3936AC3B3A81575C7B7FD307B19FE651CDE5513DD0B00ECFB34005BC69314"}, "ui": {"developer_mode": "F1A9A5CFB1A69935EB98255171E9C799EE49EFF832BA073E33FE0ECD3C5844BF"}}, "google": {"services": {"last_signed_in_username": "C34D7D2AC30B6C1BBDC3CA5A28AEE6130B3249F265FEC3BE2C2BE7005807A0FD"}}, "homepage": "E9B971698DDF2CBB043A738069FC53D537BF5BF9FE70D0903E296571E6D20BC3", "homepage_is_newtabpage": "BC18A2C9B3C8FAAD6369EA251040666ED15ED5E8C72468AFE797A1B8746D949D", "media": {"cdm": {"origin_data": "F1152C376A3F8CBED52232D93408997FDFCE2B4874AB7921822BB14824C3EFCF"}, "storage_id_salt": "111CEA848490E4664EB4C2CD813E47E4FD8883D90E4455767ADF874F5EA1EC54"}, "pinned_tabs": "9F2B95B63ED21EC3181BF8A64D3E3210A930FE6BE48B71EBEB30F33D2EC62977", "prefs": {"preference_reset_time": "A7FC47AA083ABF049F81253286165B742EAB8B65CAA12A9CA79ABD6BB152B758"}, "safebrowsing": {"incidents_sent": "42E658D4D2F225DBD53BC233708CF4CFE09495F82FFE8ED1C94E9444553BD534"}, "schedule_to_flush_to_disk": "54E20C41396F4181C6AB89DC450C734372ED535E13BF5DB146D2003F14F6D616", "search_provider_overrides": "35ED844370900963C4E865E1EE98C87C1F1463E1DE0894E738611A801B1208D9", "session": {"restore_on_startup": "293DA88517A05871CE2ED66DDB345967D1A8C217C564C5CF4BF49C2908893CFE", "startup_urls": "D521BA4EDC344413FFA4331378E746B262324E22EE8EF721E7F302E6C4605E7E"}}, "super_mac": "19DB1F7943BC28C0CDC16E53CDF2741B2BE3A6BB25000A9353F356B75F098CD3"}, "session": {"restore_on_startup": 1, "restore_on_startup_edge_enclave": "E900000001000000010100000200000078AB3A9588F16C63CB80507B1BDB2B2F4D0E35DD1D97B0247A0A12FE09760C5EE7FE0F574302C1A8BF9536B1B0157D2C0300000000000000F8A051C46271405EBBB8F49F69AC02740B7BD647EC8249A09EFDC59CD22498895994F30058CF2F220DB479F3E5BAF96B6D3CD1B6E1712264E092955FB2ADCC621106B773B40572F7D02A0CFF794B00E10E1331BBA27AB0619DB8187376744A5C407C7E1F251E4312B56F1161F9E856625A46FC4386A840E5BCAAC98F066A0A0265000000000000000200000000000000000000001000000005000000AF95F82A76", "restore_on_startup_edge_enclave_verify": "84d79ae04237c98a443bbb0b299bdf21", "startup_urls": ["http://search.babylon.com/?affID=110819&tt=060612_6_&babsrc=HP_ss&mntrId=02f83607000000000000d0df9a497059", "http://start.funmoods.com/?f=1&a=ironpub&chnl=ironpub&cd=2XzuyEtN2Y1L1Qzu0DtD0D0Fzy0AyEzyyBtDyDzytAyCtDyBtN0D0Tzu0StByEyDtN1L2XzutBtFtCtFtCtFtAtCtB&cr=1841451143", "http://search.conduit.com/?ctid=CT3027459&SearchSource=48", "http://www.searchnu.com/102", "http://feed.snapdo.com/?publisher=AirInstallerND&dpid=AirInstallerND&co=BR&userid=32b309e2-0138-8a85-d1db-0135e8d5a288&searchtype=hp&installDate={installDate}", "http://feed.snapdo.com/?publisher=AirInstallerND&dpid=AirInstallerND&co=BR&userid=32b309e2-0138-8a85-d1db-0135e8d5a288&searchtype=hp&installDate=12/11/2013", "http://www.search.ask.com/?o=APN11459&gct=hp&d=488-210&v=a13277-351&t=4", "http://www.mystartsearch.com/?type=hp&ts=1413899066&from=amt&uid=ST750LM022XHN-M750MBB_S317J90DB08133B08133", "http://www.amisites.com/?type=hp&ts=1480330584&z=23f932a292124b76276aab4gaz7b9e5c2cce8w9b8o&from=archer1028&uid=KINGSTONXSV300S37A240G_50026B7254019872"], "startup_urls_edge_enclave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startup_urls_edge_enclave_verify": "0fba5a1de7ddccce74605aba448edfae"}}