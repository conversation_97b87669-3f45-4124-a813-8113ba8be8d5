{"checksum": "3b330b1163a8d599d84f186010cc3554", "roots": {"bookmark_bar": {"children": [{"date_added": "13208183847000000", "date_last_used": "0", "guid": "3aee0527-79c0-4b77-af40-aa61e9696570", "id": "148", "name": "Introdução", "show_icon": false, "source": "sync", "type": "url", "url": "https://www.mozilla.org/pt-BR/firefox/central/", "visit_count": 0}, {"date_added": "13219470644000000", "date_last_used": "13344296375978677", "guid": "40938aa7-f896-4aec-9e5d-58bd990fd9b6", "id": "149", "name": "Python Coding for Minecraft: 18 Steps (with Pictures)", "show_icon": false, "source": "sync", "type": "url", "url": "https://www.instructables.com/id/Python-coding-for-Minecraft/", "visit_count": 0}, {"children": [], "date_added": "13231900602703506", "date_last_used": "0", "date_modified": "13231900602703506", "guid": "fb022fcf-99d9-4afb-a9fa-1e3e91650235", "id": "150", "name": "Barra de Favoritos", "source": "unknown", "type": "folder"}, {"date_added": "13226430190078999", "date_last_used": "0", "guid": "9a8b8b1e-ee03-4961-b0a9-1d0b32db7475", "id": "151", "name": "CG Persia  Page 6", "show_icon": false, "source": "sync", "type": "url", "url": "http://cgpersia.com/page/6", "visit_count": 0}, {"date_added": "13226430190151999", "date_last_used": "13383509175158430", "guid": "e5a8dca8-1509-4ab0-8879-71b9b646b950", "id": "152", "name": "<PERSON>", "show_icon": false, "source": "sync", "type": "url", "url": "http://go.microsoft.com/fwlink/p/?LinkId=255142", "visit_count": 1}, {"date_added": "13226430190119000", "date_last_used": "13356620169378323", "guid": "e75dc4c6-13bd-46b6-bfd8-0686728d3209", "id": "153", "name": "go - Golang - kill process by name - Stack Overflow", "show_icon": false, "source": "sync", "type": "url", "url": "https://stackoverflow.com/questions/41060457/golang-kill-process-by-name", "visit_count": 0}, {"date_added": "13226430190029000", "date_last_used": "0", "guid": "20502031-d645-4390-bb6d-71e8655875c4", "id": "154", "name": "Awesome Go", "show_icon": false, "source": "sync", "type": "url", "url": "https://awesome-go.com/", "visit_count": 0}, {"date_added": "13226430190044000", "date_last_used": "0", "guid": "8da53170-4510-42dd-be6e-1a11359d3fb2", "id": "155", "name": "Mickey - Desen<PERSON> para Colorir", "show_icon": false, "source": "sync", "type": "url", "url": "https://www.desenhosecolorir.com.br/imprimir/7708", "visit_count": 0}, {"date_added": "13226430190011997", "date_last_used": "0", "guid": "a811254f-8be9-4ee9-a7bd-be2eecd5ed64", "id": "156", "name": "<PERSON><PERSON>nd Face<PERSON>K - Torrent Search Web", "show_icon": false, "source": "sync", "type": "url", "url": "http://torrentsearchweb.ws/Luxand%20FaceSDK/1-0-0/", "visit_count": 0}, {"date_added": "13226430190099998", "date_last_used": "0", "guid": "5331d4eb-6725-4e28-b7a2-fd5eead46c2b", "id": "157", "name": "Google", "show_icon": false, "source": "sync", "type": "url", "url": "https://www.google.com.br/", "visit_count": 0}, {"children": [{"date_added": "13220242281538666", "date_last_used": "0", "guid": "bf8b281b-b5c9-4fe6-ace8-d1121450ebe4", "id": "159", "name": "Support Dell Com", "show_icon": false, "source": "sync", "type": "url", "url": "http://support.dell.com/support/index.aspx?c=br&l=pt&s=gen", "visit_count": 0}, {"date_added": "13218669753918294", "date_last_used": "0", "guid": "0125e03f-ffac-4ee7-9416-990e94b64b6e", "id": "160", "name": "Dell", "show_icon": false, "source": "sync", "type": "url", "url": "http://www1.la.dell.com/content/default.aspx?c=br&l=pt&s=gen", "visit_count": 0}, {"date_added": "13384885484786088", "date_last_used": "0", "guid": "696f6a5a-472a-4c9b-80bd-54cf1b2969e3", "id": "161", "name": "Support.Dell.Com", "show_icon": false, "source": "sync", "type": "url", "url": "http://www.dell.com/support/home", "visit_count": 0}], "date_added": "13231900602716444", "date_last_used": "0", "date_modified": "13384885484786088", "guid": "d802050e-2609-4117-bd00-b6e76581a440", "id": "158", "name": "Dell", "source": "unknown", "type": "folder"}, {"children": [{"date_added": "13220242281576426", "date_last_used": "0", "guid": "a18eaf8b-ad8b-4e2b-90a3-5e2d1b5b3f71", "id": "163", "name": "Lenovo", "show_icon": false, "source": "sync", "type": "url", "url": "http://www.lenovo.com/", "visit_count": 0}, {"date_added": "13220242281576426", "date_last_used": "0", "guid": "9c9e52dd-7450-4a74-b9b6-7e36a0688524", "id": "164", "name": "Lenovo Support", "show_icon": false, "source": "sync", "type": "url", "url": "http://support.lenovo.com/", "visit_count": 0}, {"date_added": "13250805122556619", "date_last_used": "0", "guid": "3c4d165d-b240-48b9-a849-48b8f630e81c", "id": "165", "name": "appwrite/awesome-appwrite: Carefully curated list of awesome Appwrite resources 💪", "show_icon": false, "source": "sync", "type": "url", "url": "https://github.com/appwrite/awesome-appwrite", "visit_count": 0}, {"date_added": "13253211378796392", "date_last_used": "0", "guid": "e667a1ca-0233-4b80-bb8c-6fef6c7bfb65", "id": "166", "name": "D", "show_icon": false, "source": "sync", "type": "url", "url": "https://web.whatsapp.com/", "visit_count": 0}, {"date_added": "13255550235750167", "date_last_used": "0", "guid": "b18800ea-4e00-4f7e-9505-821e5d832c74", "id": "167", "name": "JSON to Dart", "show_icon": false, "source": "sync", "type": "url", "url": "https://javiercbk.github.io/json_to_dart/", "visit_count": 0}, {"date_added": "13256439375217316", "date_last_used": "0", "guid": "7cdc9118-dfd3-49da-bec1-245a97419461", "id": "168", "name": "(359) O QUE NÃO PODE FALTAR NO SEU ECOMMERCE [2021] - YouTube", "show_icon": false, "source": "sync", "type": "url", "url": "https://www.youtube.com/watch?v=QJ0FE4kpMGA", "visit_count": 0}, {"date_added": "13256864957313694", "date_last_used": "0", "guid": "15f30fda-d0d1-43ba-b2f0-80a819af32db", "id": "169", "name": "UNHIDE School | Planejando e Produzindo Jogos Digitais", "show_icon": false, "source": "sync", "type": "url", "url": "https://www.unhideschool.com/home/<USER>/3062/planejando-e-produzindo-jogos-digitais", "visit_count": 0}, {"date_added": "13257431880290803", "date_last_used": "0", "guid": "43d7c1d9-8575-4aa2-b277-5eee5bd34051", "id": "170", "name": "O criador de formulários online mais acessível", "show_icon": false, "source": "sync", "type": "url", "url": "https://www.jotform.com/build/210386371280654", "visit_count": 0}, {"date_added": "13258685664935495", "date_last_used": "0", "guid": "df2e834a-e993-4eac-b63b-da90ffceb1a4", "id": "171", "name": "@fmf", "show_icon": false, "source": "sync", "type": "url", "url": "https://discord.com/channels/@me/742427529725345852", "visit_count": 0}, {"date_added": "13258950518684048", "date_last_used": "0", "guid": "5c6a0e48-76ff-4b5c-8bf9-8e313b3cfb18", "id": "172", "name": "Daz 3D ALL PRODUCTS 2020 - GoDownloads", "show_icon": false, "source": "sync", "type": "url", "url": "https://godownloads.net/daz-3d-all-products-2020/", "visit_count": 0}, {"date_added": "13259070749356410", "date_last_used": "0", "guid": "499f246f-ec17-464a-9fa2-6779508c4edd", "id": "173", "name": "Como compilar e implantar um aplicativo Flask usando o Docker no Ubuntu 18.04 | DigitalOcean", "show_icon": false, "source": "sync", "type": "url", "url": "https://www.digitalocean.com/community/tutorials/how-to-build-and-deploy-a-flask-application-using-docker-on-ubuntu-18-04-pt", "visit_count": 0}, {"date_added": "13259547704700765", "date_last_used": "0", "guid": "55676e90-d231-4ccf-aca8-92d772e9e257", "id": "174", "name": "Voolt3D | Imprimindo o mundo", "show_icon": false, "source": "sync", "type": "url", "url": "https://www.lojavoolt3d.com.br/", "visit_count": 0}], "date_added": "13231900602716974", "date_last_used": "0", "date_modified": "13259547704700765", "guid": "8fa26e27-f4f4-485f-babf-c4d628fd4d90", "id": "162", "name": "Lenovo", "source": "unknown", "type": "folder"}, {"date_added": "13218669753948295", "date_last_used": "0", "guid": "65033539-dd68-4b78-ba72-64b69261f23f", "id": "175", "name": "6 Best Torrent Search Engine Sites To Find Your Favorite Torrents   2018 Edition", "show_icon": false, "source": "sync", "type": "url", "url": "https://fossbytes.com/best-torrent-search-engine/", "visit_count": 0}, {"date_added": "13218669753954294", "date_last_used": "13393794287017811", "guid": "885586bc-d622-4772-81ec-68d55ded26e0", "id": "176", "name": "Deep Fakes ao vivo — que tal entrar em uma video conferência com o rosto de outra pessoa  !", "show_icon": false, "source": "sync", "type": "url", "url": "https://medium.com/ensina-ai/deep-fakes-ao-vivo-que-tal-entrar-em-uma-video-confer%C3%AAncia-com-o-rosto-de-outra-pessoa-92fc8c2d5726", "visit_count": 3}, {"date_added": "*****************", "date_last_used": "0", "guid": "915a5e6f-d950-4ce2-926e-3813f795edf7", "id": "177", "name": "Free Multiple Cloud Storage Manager  Manage Multiple Cloud Storage Accounts   MultCloud", "show_icon": false, "source": "sync", "type": "url", "url": "https://www.multcloud.com/#home", "visit_count": 0}, {"date_added": "*****************", "date_last_used": "0", "guid": "7ca370fa-459d-40f1-a1ba-5d61eca6eb2c", "id": "178", "name": "MultCloud - Put multiple cloud drives into one", "show_icon": false, "source": "sync", "type": "url", "url": "https://www.multcloud.com/index.jsp?rl=en-US#home", "visit_count": 0}, {"date_added": "*****************", "date_last_used": "0", "guid": "d17bcf84-5038-4e1b-8e85-6deec0a94a9b", "id": "179", "name": "PROGRAMAS   DTorrent", "show_icon": false, "source": "sync", "type": "url", "url": "http://www.dtorrent.com.br/category/programas/page/11/", "visit_count": 0}, {"date_added": "*****************", "date_last_used": "0", "guid": "ef275de1-4903-4857-a37d-ca0c18955bad", "id": "180", "name": "UI Kits, Icons, Templates, Themes and More - UpLabs", "show_icon": false, "source": "sync", "type": "url", "url": "https://www.uplabs.com/categories/ios?sort=latest", "visit_count": 0}, {"date_added": "0", "date_last_used": "0", "guid": "967a56b8-0369-48fb-ab2d-ffc3c3c04372", "id": "181", "name": "Prime<PERSON>s passos", "show_icon": false, "source": "sync", "type": "url", "url": "https://www.mozilla.org/pt-BR/firefox/central/", "visit_count": 0}, {"date_added": "0", "date_last_used": "0", "guid": "c14e4535-3d63-44a3-a287-96a0aac1b2d7", "id": "182", "name": "Seus prezis | Prezi", "show_icon": false, "source": "sync", "type": "url", "url": "http://prezi.com/your/", "visit_count": 0}, {"children": [{"date_added": "0", "date_last_used": "0", "guid": "347acb05-2ee4-4357-91d1-ddbaa682a8b6", "id": "184", "name": "Enterprise Integration Patterns - Solving Integration Problems using Patterns", "show_icon": false, "source": "sync", "type": "url", "url": "http://www.eaipatterns.com/Chapter1.html", "visit_count": 0}], "date_added": "13274928000181379", "date_last_used": "0", "date_modified": "13274928000181379", "guid": "295def0e-e4f9-44b3-9a33-e002c060fced", "id": "183", "name": "livro para aula UFG", "source": "unknown", "type": "folder"}, {"children": [{"children": [{"children": [{"children": [{"date_added": "0", "date_last_used": "0", "guid": "aa045e0f-869c-4610-92d5-7f9d68172984", "id": "189", "name": "Listão de Revistas | Retroavengers", "show_icon": false, "source": "sync", "type": "url", "url": "http://retroavengers.blogspot.com.br/p/listao-de-revistas_28.html", "visit_count": 0}, {"date_added": "0", "date_last_used": "0", "guid": "2c4d3b92-652e-43e8-bb22-cb8126491b42", "id": "190", "name": "Arrow – Temporada 2.5 :HQ Online", "show_icon": false, "source": "sync", "type": "url", "url": "http://hqonline.com.br/?page_id=38349", "visit_count": 0}, {"children": [{"date_added": "0", "date_last_used": "0", "guid": "c7121bff-f7d9-46ec-b7d7-a0acf195aba0", "id": "192", "name": "Android Asset Studio - Icon Generator - Launcher icons", "show_icon": false, "source": "sync", "type": "url", "url": "http://romannurik.github.io/AndroidAssetStudio/icons-launcher.html#foreground.type=image&foreground.space.trim=1&foreground.space.pad=0&foreColor=607d8b%2C0&crop=0&backgroundShape=square&backColor=ffffff%2C100&effects=none", "visit_count": 0}, {"date_added": "0", "date_last_used": "0", "guid": "04e4c911-bb62-4504-bff3-a332290e627d", "id": "193", "name": "Curso Iniciantes na UE4 - Login", "show_icon": false, "source": "sync", "type": "url", "url": "https://cursodidaticoparainiciantesnaunrealengine.club.hotmart.com/login.html", "visit_count": 0}, {"date_added": "0", "date_last_used": "0", "guid": "f42da624-126d-4a6a-adf7-c7327c69ad00", "id": "194", "name": "Curso Iniciantes na UE4", "show_icon": false, "source": "sync", "type": "url", "url": "https://cursodidaticoparainiciantesnaunrealengine.club.hotmart.com/member/content.html", "visit_count": 0}, {"date_added": "0", "date_last_used": "0", "guid": "6032cd43-5b28-4659-b671-e3fe64851514", "id": "195", "name": "Home - Android Accelerate", "show_icon": false, "source": "sync", "type": "url", "url": "http://membros.androidaccelerate.com.br/", "visit_count": 0}, {"date_added": "0", "date_last_used": "0", "guid": "630f066a-9fe8-4092-a8e3-0da8b53f78cb", "id": "196", "name": "MIFARE SDK Lite | MIFARE", "show_icon": false, "source": "sync", "type": "url", "url": "https://www.mifare.net/en/products/tools/mifare-sdk/mifare-sdk-lite/", "visit_count": 0}, {"date_added": "0", "date_last_used": "0", "guid": "42328141-0db2-49f1-91bd-1338572747ba", "id": "197", "name": "Área de Aluno - Curso Aprenda Unity", "show_icon": false, "source": "sync", "type": "url", "url": "http://aprendaunity.com.br/intranet/sistema.php", "visit_count": 0}, {"date_added": "0", "date_last_used": "0", "guid": "1e499419-a3bb-4df5-b1c2-6d76c88e4fc1", "id": "198", "name": "keras/cifar10_cnn.py at master · fchollet/keras · GitHub", "show_icon": false, "source": "sync", "type": "url", "url": "https://github.com/fchollet/keras/blob/master/examples/cifar10_cnn.py", "visit_count": 0}, {"date_added": "0", "date_last_used": "0", "guid": "5aea8de6-354b-41e5-a529-083305192cb0", "id": "199", "name": "Browse Torrents :: CGPeers Beta 2 Build 723423 32bit", "show_icon": false, "source": "sync", "type": "url", "url": "https://www.cgpeers.to/torrents.php", "visit_count": 0}, {"date_added": "0", "date_last_used": "0", "guid": "fda80a3e-d4bf-4b56-9922-3e6d26162668", "id": "200", "name": "Full 360 stereoscopic video playback in Unity | <PERSON>", "show_icon": false, "source": "sync", "type": "url", "url": "http://bernieroehl.com/360stereoinunity/", "visit_count": 0}, {"date_added": "0", "date_last_used": "0", "guid": "c68bfcc1-eedd-438a-9502-3cc22b6f41fc", "id": "201", "name": "Stanford University CS231n: Course Projects Winter 2016", "show_icon": false, "source": "sync", "type": "url", "url": "http://cs231n.stanford.edu/reports2016.html", "visit_count": 0}, {"date_added": "0", "date_last_used": "0", "guid": "43b509df-6da4-4a33-a1fd-7fca708baf60", "id": "202", "name": "LNCC - Laboratório Nacional de Computação Científica", "show_icon": false, "source": "sync", "type": "url", "url": "http://www.lncc.br/eventoSeminario/cursos.php?idt_tipo_atividade=12&idt_evento=846&descAtiv=Mini-Cursos#2502", "visit_count": 0}, {"date_added": "0", "date_last_used": "0", "guid": "1c5ef8a7-62ec-4539-8095-ebcdebdc7cd8", "id": "203", "name": "Pacote de ROMs para RetroPie e Recalbox na Raspberry Pi ~ Blog do Ruivo - Games | RuivoPlay", "show_icon": false, "source": "sync", "type": "url", "url": "http://jogoretro.blogspot.com/2018/05/retropie-recalbox-roms.html", "visit_count": 0}, {"date_added": "0", "date_last_used": "0", "guid": "3d62b931-043c-4065-b81e-3a4eb072f9db", "id": "204", "name": "Orange Pi PC ArcadeRetro1.1 imagem 32GB RetrOrangepi 4.2 Full + Kodi + GPIO", "show_icon": false, "source": "sync", "type": "url", "url": "http://www.orangepiretro.tk/2019/01/orange-pi-pc-arcaderetro11-imagem-32gb.html", "visit_count": 0}, {"date_added": "0", "date_last_used": "0", "guid": "9a5ab518-39c7-4ab6-b934-6ca71c22dfb1", "id": "205", "name": "Como fazer um Videogame Retrô com MENOS de 100 REAIS ~ Blog do Ruivo - Games | RuivoPlay", "show_icon": false, "source": "sync", "type": "url", "url": "http://jogoretro.blogspot.com/2018/05/videogame-retro-barato.html", "visit_count": 0}], "date_added": "13274928000181815", "date_last_used": "0", "date_modified": "13274928000181815", "guid": "81eb83f2-af67-4b16-83a4-f29e789f09c2", "id": "191", "name": "android", "source": "unknown", "type": "folder"}], "date_added": "13274928000181724", "date_last_used": "0", "date_modified": "13274928000181724", "guid": "ccb40540-3446-424c-a25e-55639ffb36c2", "id": "188", "name": "livro para aula UFG", "source": "unknown", "type": "folder"}], "date_added": "13274928000181713", "date_last_used": "0", "date_modified": "13274928000181713", "guid": "58e1997e-7b59-43c5-b4db-e51edcc3a3e2", "id": "187", "name": "Importado do Chrome", "source": "unknown", "type": "folder"}, {"date_added": "0", "date_last_used": "0", "guid": "6db67c06-3221-4163-a7c3-1cc2ba205e72", "id": "206", "name": "Forest Friends Greeting Card by <PERSON><PERSON><PERSON>", "show_icon": false, "source": "sync", "type": "url", "url": "https://www.facebook.com/media/set/?set=a.10151301415232067.555528.109879932066&type=3", "visit_count": 0}, {"date_added": "0", "date_last_used": "0", "guid": "f24bdd39-69ee-4d32-9932-20df036899f0", "id": "207", "name": "Silhouetteira Voadora: Novembro 2012", "show_icon": false, "source": "sync", "type": "url", "url": "http://silhouetteiravoadora.blogspot.com.br/2012_11_01_archive.html", "visit_count": 0}, {"date_added": "0", "date_last_used": "0", "guid": "2306f524-572e-4a2d-813c-d712019525c6", "id": "208", "name": "Prime<PERSON>s passos", "show_icon": false, "source": "sync", "type": "url", "url": "http://www.mozilla.com/pt-BR/firefox/central/", "visit_count": 0}, {"date_added": "0", "date_last_used": "0", "guid": "865b286c-2ea8-4b64-a941-447f86c08914", "id": "209", "name": "Podcast Inglesonline — Inglês Online", "show_icon": false, "source": "sync", "type": "url", "url": "http://www.inglesonline.com.br/category/podcast-inglesonline/", "visit_count": 0}, {"date_added": "0", "date_last_used": "0", "guid": "cc57e747-5db5-450b-8508-ae07f77f6522", "id": "210", "name": "netfabb Cloud Service - Beta", "show_icon": false, "source": "sync", "type": "url", "url": "http://cloud.netfabb.com/", "visit_count": 0}, {"date_added": "0", "date_last_used": "0", "guid": "9ae30155-f32c-412e-a8f2-44d708d6677c", "id": "211", "name": "Lunch With Mom SVG Kit", "show_icon": false, "source": "sync", "type": "url", "url": "https://www.facebook.com/media/set/?set=a.10150270101317067.390415.109879932066&type=3", "visit_count": 0}, {"date_added": "0", "date_last_used": "0", "guid": "69760ed5-f168-436a-b708-da4783ed6fe1", "id": "212", "name": "Suporte Cupcake em camadas <PERSON> | SVGCuts.com Blog", "show_icon": false, "source": "sync", "type": "url", "url": "http://svgcuts.com/blog/2012/10/25/tiered-cupcake-stand-by-thienly-azim/", "visit_count": 0}, {"date_added": "0", "date_last_used": "0", "guid": "7661cea7-185b-4db3-aac8-825b6278b775", "id": "213", "name": "Elementos UML", "show_icon": false, "source": "sync", "type": "url", "url": "http://docs.kde.org/stable/pt_BR/kdesdk/umbrello/uml-elements.html#use-case-diagram", "visit_count": 0}, {"date_added": "0", "date_last_used": "0", "guid": "589dca30-ed29-4384-b634-e96ff53a44f1", "id": "214", "name": "PAPEIS FINOS By Diamond Papers", "show_icon": false, "source": "sync", "type": "url", "url": "http://www.papeisfinos.com.br/produtos-index/categorias/373121/folhas_a4.html", "visit_count": 0}, {"date_added": "0", "date_last_used": "0", "guid": "38fed22a-fe28-4b8c-85cf-471c74299e51", "id": "215", "name": "Doggie House Gift Box by <PERSON><PERSON><PERSON><PERSON>", "show_icon": false, "source": "sync", "type": "url", "url": "https://www.facebook.com/media/set/?set=a.10151252373772067.549998.109879932066&type=3", "visit_count": 0}, {"date_added": "0", "date_last_used": "0", "guid": "3334b8f3-cea1-4c2a-b3d7-959b4cbe3f56", "id": "216", "name": "Craftideias - papel Color Plus", "show_icon": false, "source": "sync", "type": "url", "url": "http://www.craftideias.com.br/papeis-1/color-plus.html", "visit_count": 0}, {"date_added": "0", "date_last_used": "0", "guid": "94d24460-eb6c-4d40-af47-67e84ede8b55", "id": "217", "name": "w4sf6l.rar - Free Download from HotfileSearch.com", "show_icon": false, "source": "sync", "type": "url", "url": "http://www.hotfilesearch.com/download/27366557-w4sf6l-rar.html", "visit_count": 0}, {"date_added": "0", "date_last_used": "0", "guid": "8eb9f232-5c38-4451-a9d1-56da445ef79b", "id": "218", "name": "Últimas notícias", "show_icon": false, "source": "sync", "type": "url", "url": "http://www.estadao.com.br/", "visit_count": 0}, {"date_added": "0", "date_last_used": "0", "guid": "addcc71b-9082-430f-ba3d-8d7d85e28b4a", "id": "219", "name": "Peony Cottage SVG Kit", "show_icon": false, "source": "sync", "type": "url", "url": "https://www.facebook.com/media/set/?set=a.10150314769092067.410498.109879932066&type=3", "visit_count": 0}, {"date_added": "0", "date_last_used": "0", "guid": "e70246ca-5ec9-4123-8651-360012a52cea", "id": "220", "name": "Gingerbread Peony Cottage - Amy's Corner", "show_icon": false, "source": "sync", "type": "url", "url": "https://www.facebook.com/media/set/?set=a.10150324498472067.413962.109879932066&type=3", "visit_count": 0}, {"date_added": "0", "date_last_used": "0", "guid": "c0ebcf29-6df5-471f-9bbe-26ca15104b15", "id": "221", "name": "Fazendo a Minha Festa!: <PERSON><PERSON><PERSON> de Kits Completos", "show_icon": false, "source": "sync", "type": "url", "url": "http://fazendoaminhafesta.blogspot.com.br/2011/09/indice-de-kits-completos.html", "visit_count": 0}, {"date_added": "0", "date_last_used": "0", "guid": "fac0ce41-2e00-4326-9cca-021e611e2a95", "id": "222", "name": "Introdução ao Studio GameMaker: Parte 1", "show_icon": false, "source": "sync", "type": "url", "url": "http://www.3dmotive.com/training/gamemaker-studio/the-intro-to-gamemaker-studio-part-1/?follow=true", "visit_count": 0}, {"date_added": "0", "date_last_used": "0", "guid": "19c6593a-c987-40c9-b01d-bf09be79e731", "id": "223", "name": "Tea For You and Me SVG Kit", "show_icon": false, "source": "sync", "type": "url", "url": "https://www.facebook.com/media/set/?set=a.10151108034817067.533839.109879932066&type=3", "visit_count": 0}, {"date_added": "0", "date_last_used": "0", "guid": "03de1b5a-e954-4635-b5ed-ec68bbccfe9c", "id": "224", "name": "Unity 3D Video Tutorials | 100% Free | Category: Examples | The Best Way to Learn Video Game Development", "show_icon": false, "source": "sync", "type": "url", "url": "http://www.unity3dstudent.com/category/examples/", "visit_count": 0}, {"date_added": "0", "date_last_used": "0", "guid": "0e1b8253-c6a7-449d-bbc0-532e11ec4b5c", "id": "225", "name": "Peony Cottage SVG Kit - Fan Gallery", "show_icon": false, "source": "sync", "type": "url", "url": "https://www.facebook.com/media/set/?set=a.10150313817672067.410124.109879932066&type=3", "visit_count": 0}, {"date_added": "0", "date_last_used": "0", "guid": "b3273a62-a297-4e1c-9df4-44bc542d54da", "id": "226", "name": "OMultiPanel - the ultimate TSplitter replacement for Delphi & Lazarus", "show_icon": false, "source": "sync", "type": "url", "url": "http://www.kluug.net/omultipanel.php", "visit_count": 0}, {"date_added": "0", "date_last_used": "0", "guid": "58e6efc4-f968-4bd9-a425-82953a71413d", "id": "227", "name": "GitHub - kuz/DeepMind-Atari-Deep-Q-Learner: The original code from the DeepMind article + my tweaks", "show_icon": false, "source": "sync", "type": "url", "url": "https://github.com/kuz/DeepMind-Atari-Deep-Q-Learner", "visit_count": 0}, {"date_added": "0", "date_last_used": "0", "guid": "7b204bb5-3d19-41e9-bc0e-0776518959cf", "id": "228", "name": "da<PERSON><PERSON><PERSON><PERSON><PERSON>/dqn - <PERSON><PERSON>", "show_icon": false, "source": "sync", "type": "url", "url": "https://hub.docker.com/r/darshanhegde/dqn/", "visit_count": 0}, {"date_added": "0", "date_last_used": "0", "guid": "fd207217-4a02-4619-9567-120104a6dac6", "id": "229", "name": "hmlatapie/dqn_vnc - <PERSON><PERSON> Hub", "show_icon": false, "source": "sync", "type": "url", "url": "https://hub.docker.com/r/hmlatapie/dqn_vnc/builds/", "visit_count": 0}], "date_added": "13274928000181669", "date_last_used": "0", "date_modified": "13274928000181669", "guid": "4297d67e-2278-4baf-9785-11a3133ffb3a", "id": "186", "name": "Barra dos favoritos", "source": "unknown", "type": "folder"}, {"children": [{"date_added": "0", "date_last_used": "0", "guid": "5c44bdc6-2708-4a79-a631-d82585078f91", "id": "231", "name": "Ajuda e dicas", "show_icon": false, "source": "sync", "type": "url", "url": "http://www.mozilla.com/pt-BR/firefox/help/", "visit_count": 0}, {"date_added": "0", "date_last_used": "0", "guid": "467b6033-c9a5-4114-8ef8-37d8ea6ddbc3", "id": "232", "name": "Participe", "show_icon": false, "source": "sync", "type": "url", "url": "http://www.mozilla.com/pt-BR/firefox/community/", "visit_count": 0}, {"date_added": "0", "date_last_used": "0", "guid": "fb14da54-ee7f-4519-a06b-91e30fa610de", "id": "233", "name": "Personalize o Firefox", "show_icon": false, "source": "sync", "type": "url", "url": "http://www.mozilla.com/pt-BR/firefox/customize/", "visit_count": 0}, {"date_added": "0", "date_last_used": "0", "guid": "d043d38c-4e03-42cd-8b78-36852f7a70bd", "id": "234", "name": "Sobre nós", "show_icon": false, "source": "sync", "type": "url", "url": "http://www.mozilla.com/pt-BR/about/", "visit_count": 0}], "date_added": "13274928000183273", "date_last_used": "0", "date_modified": "13274928000183273", "guid": "b18a0d16-d892-47b3-b6ec-df5f47512604", "id": "230", "name": "Mozilla Firefox", "source": "unknown", "type": "folder"}, {"children": [{"children": [{"date_added": "0", "date_last_used": "0", "guid": "071f9370-de41-4f43-b39c-2fd6cc35a5e1", "id": "237", "name": "Livros EBooks Download", "show_icon": false, "source": "sync", "type": "url", "url": "http://www.baixebr.org/category/livros/", "visit_count": 0}], "date_added": "13274928000183421", "date_last_used": "0", "date_modified": "13274928000183421", "guid": "97f31f45-c609-4cde-a5a8-a835fdd22529", "id": "236", "name": "livros", "source": "unknown", "type": "folder"}], "date_added": "13274928000183409", "date_last_used": "0", "date_modified": "13274928000183409", "guid": "b8c8a6d0-2523-4d15-8fb6-4013aa7645d9", "id": "235", "name": "Não organizados", "source": "unknown", "type": "folder"}], "date_added": "13274928000181549", "date_last_used": "0", "date_modified": "13274928000181549", "guid": "9f5deac0-745c-4435-b3b6-442bd5736b63", "id": "185", "name": "<PERSON><PERSON><PERSON><PERSON>", "source": "unknown", "type": "folder"}, {"children": [{"children": [], "date_added": "13274928000183616", "date_last_used": "0", "date_modified": "13274928000183616", "guid": "a2a2daf4-c4e1-4281-aa7c-c3742a7ca407", "id": "239", "name": "Links", "source": "unknown", "type": "folder"}, {"children": [{"date_added": "0", "date_last_used": "0", "guid": "36efdf47-e0c1-4a33-9ef8-32f262d7d747", "id": "241", "name": "AndEngine » AndEngineExamples - Android Game Engine", "show_icon": false, "source": "sync", "type": "url", "url": "http://www.andengine.org/blog/category/andengineexamples/", "visit_count": 0}], "date_added": "13274928000183629", "date_last_used": "0", "date_modified": "13274928000183629", "guid": "218f6658-c2e6-4a60-aa96-87ea2c375866", "id": "240", "name": "desenvolvimento Android", "source": "unknown", "type": "folder"}, {"children": [{"children": [{"date_added": "0", "date_last_used": "0", "guid": "8b7726e3-97e2-4c6c-bf91-9f6058311191", "id": "244", "name": "Zinio Reader 4", "show_icon": false, "source": "sync", "type": "url", "url": "http://redirect.hp.com/svs/rdr?TYPE=4&s=zinio&tp=iefavs&pf=cndt&locale=pt_BR&bd=all&c=111", "visit_count": 0}], "date_added": "13274928000183826", "date_last_used": "0", "date_modified": "13274928000183826", "guid": "1cd119dc-7eaf-45d0-a694-bc2adb4e776c", "id": "243", "name": "eReaders", "source": "unknown", "type": "folder"}, {"date_added": "0", "date_last_used": "0", "guid": "1a59615d-dd43-44df-acdd-9c7e5fab43b0", "id": "245", "name": "HP Games", "show_icon": false, "source": "sync", "type": "url", "url": "http://redirect.hp.com/svs/rdr?TYPE=4&tp=iefavs&s=myhpgames&pf=cndt&locale=pt_BR&bd=all&c=111", "visit_count": 0}], "date_added": "13274928000183806", "date_last_used": "0", "date_modified": "13274928000183806", "guid": "35ba0df0-e206-4dfd-b59c-0a3c533953b0", "id": "242", "name": "HP", "source": "unknown", "type": "folder"}, {"children": [{"date_added": "0", "date_last_used": "0", "guid": "c658968f-e4bb-4fca-bdb2-8e5848f8cdd7", "id": "247", "name": "MSN Automóvel", "show_icon": false, "source": "sync", "type": "url", "url": "http://go.microsoft.com/fwlink/?LinkId=72680", "visit_count": 0}, {"date_added": "0", "date_last_used": "0", "guid": "0d8cdd77-31cd-4d0f-8982-11cbb9a27bca", "id": "248", "name": "MSN Esportes", "show_icon": false, "source": "sync", "type": "url", "url": "http://go.microsoft.com/fwlink/?LinkId=72635", "visit_count": 0}, {"date_added": "0", "date_last_used": "0", "guid": "9f88402b-001d-4c99-815d-2ed97a643f82", "id": "249", "name": "MSN Seu Dinheiro", "show_icon": false, "source": "sync", "type": "url", "url": "http://go.microsoft.com/fwlink/?LinkId=72640", "visit_count": 0}, {"date_added": "0", "date_last_used": "0", "guid": "32678ade-c759-4514-9fc5-6b91ea1727ef", "id": "250", "name": "MSN", "show_icon": false, "source": "sync", "type": "url", "url": "http://go.microsoft.com/fwlink/?LinkId=72630", "visit_count": 0}, {"date_added": "0", "date_last_used": "0", "guid": "8567e0dd-58f5-44c2-95ab-569b02e2d9bb", "id": "251", "name": "MSN Entretenimento", "show_icon": false, "source": "sync", "type": "url", "url": "http://go.microsoft.com/fwlink/?LinkId=72659", "visit_count": 0}, {"date_added": "0", "date_last_used": "0", "guid": "37e5f304-ccfb-461a-94f0-e0e048b77626", "id": "252", "name": "MSN Notícias", "show_icon": false, "source": "sync", "type": "url", "url": "http://go.microsoft.com/fwlink/?LinkId=72636", "visit_count": 0}], "date_added": "13274928000184008", "date_last_used": "0", "date_modified": "13274928000184008", "guid": "d0f35329-b7b4-42f5-a768-e4b0240914b2", "id": "246", "name": "MSN", "source": "unknown", "type": "folder"}, {"children": [{"date_added": "0", "date_last_used": "0", "guid": "628c8f96-cca4-4dae-8d8c-d789c7bf44fa", "id": "254", "name": "<PERSON><PERSON><PERSON> da Microsoft", "show_icon": false, "source": "sync", "type": "url", "url": "http://go.microsoft.com/fwlink/?LinkId=72892", "visit_count": 0}, {"date_added": "0", "date_last_used": "0", "guid": "8c92346a-e0ce-487e-b416-ce9cf08df9fc", "id": "255", "name": "Microsoft Brasil", "show_icon": false, "source": "sync", "type": "url", "url": "http://go.microsoft.com/fwlink/?LinkId=72520", "visit_count": 0}, {"date_added": "0", "date_last_used": "0", "guid": "7ba5124f-b237-42c8-a3b2-7eb31a6b33cd", "id": "256", "name": "Site do IE na Microsoft.com", "show_icon": false, "source": "sync", "type": "url", "url": "http://go.microsoft.com/fwlink/?LinkId=72186", "visit_count": 0}, {"date_added": "0", "date_last_used": "0", "guid": "f77cf159-b8b3-44cb-bfc2-1022386504e7", "id": "257", "name": "Marketplace", "show_icon": false, "source": "sync", "type": "url", "url": "http://go.microsoft.com/fwlink/?LinkId=72411", "visit_count": 0}], "date_added": "13274928000184397", "date_last_used": "0", "date_modified": "13274928000184397", "guid": "4b1adfa0-8922-43ed-a37e-b59f2457e6fa", "id": "253", "name": "Sites da Microsoft na Web", "source": "unknown", "type": "folder"}, {"children": [{"date_added": "0", "date_last_used": "0", "guid": "5a470d60-8554-4d1f-a4be-dc251597d1cc", "id": "259", "name": "Windows Live Gallery", "show_icon": false, "source": "sync", "type": "url", "url": "http://go.microsoft.com/fwlink/?LinkID=70742", "visit_count": 0}, {"date_added": "0", "date_last_used": "0", "guid": "1a57c73f-b918-4f21-885d-9401f697afae", "id": "260", "name": "Windows Live Spaces", "show_icon": false, "source": "sync", "type": "url", "url": "http://go.microsoft.com/fwlink/?LinkId=72682", "visit_count": 0}, {"date_added": "0", "date_last_used": "0", "guid": "220b4aef-3f21-4dc5-a329-4ba6e566d241", "id": "261", "name": "Windows Live Mail", "show_icon": false, "source": "sync", "type": "url", "url": "http://go.microsoft.com/fwlink/?LinkId=72681", "visit_count": 0}, {"date_added": "0", "date_last_used": "0", "guid": "b9fdd063-4153-41da-b94a-bef65605acc0", "id": "262", "name": "Obtenha o Windows Live", "show_icon": false, "source": "sync", "type": "url", "url": "http://go.microsoft.com/fwlink/?LinkId=72700", "visit_count": 0}], "date_added": "13274928000184615", "date_last_used": "0", "date_modified": "13274928000184615", "guid": "2b450645-5ebf-4552-8308-0835a0603acb", "id": "258", "name": "Windows Live", "source": "unknown", "type": "folder"}, {"children": [], "date_added": "13274928000184870", "date_last_used": "0", "date_modified": "13274928000184870", "guid": "c82ff3d0-6190-4d6a-8a8d-029af7eac273", "id": "263", "name": "Desenvolvimento", "source": "unknown", "type": "folder"}, {"children": [{"date_added": "0", "date_last_used": "0", "guid": "f421070d-b0d6-45d6-b309-44ca3a995617", "id": "265", "name": "UniDev - Programação de Jogos • View forum - Artigos", "show_icon": false, "source": "sync", "type": "url", "url": "http://unidev.com.br/phpbb3/viewforum.php?f=82", "visit_count": 0}], "date_added": "13274928000184887", "date_last_used": "0", "date_modified": "13274928000184887", "guid": "a9b75de4-4b21-4b11-9100-6392e67649c7", "id": "264", "name": "jogos", "source": "unknown", "type": "folder"}, {"children": [], "date_added": "13274928000184976", "date_last_used": "0", "date_modified": "13274928000184976", "guid": "134fc7f4-fe64-41fe-98ad-2311a79cce8b", "id": "266", "name": "delta", "source": "unknown", "type": "folder"}, {"date_added": "0", "date_last_used": "0", "guid": "b561d172-d2f4-4bfc-ad39-744700a1a76a", "id": "267", "name": "Browse Free Virus Removal <PERSON>l Letter - BitDefender", "show_icon": false, "source": "sync", "type": "url", "url": "http://www.bitdefender.com/br/Downloads/browseFreeRemovalTool/", "visit_count": 0}, {"date_added": "0", "date_last_used": "0", "guid": "97eedc20-5028-4294-a22d-6e383a1be727", "id": "268", "name": "Comprovante de Situação Cadastral no CPF", "show_icon": false, "source": "sync", "type": "url", "url": "http://www.receita.fazenda.gov.br/Aplicacoes/ATCTA/CPF/ConsultaPublica.asp", "visit_count": 0}, {"date_added": "0", "date_last_used": "0", "guid": "528f95fc-4322-4ac1-98f7-56034e9313b9", "id": "269", "name": "<PERSON><PERSON><PERSON>", "show_icon": false, "source": "sync", "type": "url", "url": "http://www.zylom.com/", "visit_count": 0}], "date_added": "13274928000183463", "date_last_used": "0", "date_modified": "13274928000183463", "guid": "45232fc2-9d39-4539-be0e-5169f4db8862", "id": "238", "name": "Importado do IE", "source": "unknown", "type": "folder"}, {"children": [{"date_added": "0", "date_last_used": "0", "guid": "a638811c-f840-4289-b6e8-fe20141ba2df", "id": "271", "name": "Métricas para startup que todo CEO precisa conhecer", "show_icon": false, "source": "sync", "type": "url", "url": "https://meetime.com.br/blog/gestao-empresarial/metricas-para-startups/", "visit_count": 0}, {"date_added": "0", "date_last_used": "0", "guid": "deb28c38-9d9f-4f0e-87cb-9deda2b26398", "id": "272", "name": "Metabuscador de dados de pesquisa FAPESP: Página inicial", "show_icon": false, "source": "sync", "type": "url", "url": "https://metabuscador.uspdigital.usp.br/", "visit_count": 0}, {"date_added": "0", "date_last_used": "0", "guid": "3964c56e-c137-40f7-97af-cd230993ec0a", "id": "273", "name": "Getting Started - <PERSON><PERSON><PERSON><PERSON>", "show_icon": false, "source": "sync", "type": "url", "url": "https://docs.getwidget.dev/getting-started", "visit_count": 0}, {"date_added": "0", "date_last_used": "0", "guid": "63b00772-ff10-4df9-9c7b-8e25958bfaca", "id": "274", "name": "flutter", "show_icon": false, "source": "sync", "type": "url", "url": "https://flutter.dev/docs/deployment/android", "visit_count": 0}, {"date_added": "0", "date_last_used": "0", "guid": "5c998e63-486d-42e2-94dd-386f81b6e472", "id": "275", "name": "Como lançar um aplicativo de sucesso no mercado", "show_icon": false, "source": "sync", "type": "url", "url": "https://jera.com.br/blog/5801/desenvolvimento/como-lancar-um-aplicativo-de-sucesso", "visit_count": 0}, {"date_added": "0", "date_last_used": "0", "guid": "bdffb754-d953-4798-a2fa-bf5bdd8ca70a", "id": "276", "name": "Homem Aranha 1ª Série (1983/2000) Completa - Download de HQs", "show_icon": false, "source": "sync", "type": "url", "url": "https://downloaddehqs.blogspot.com/2018/11/homem-aranha-1-serie-19832000-completa.html", "visit_count": 0}, {"date_added": "0", "date_last_used": "0", "guid": "558c174b-cc3e-4ff5-99c8-295f707c7f8e", "id": "277", "name": "[DOWNLOAD] Character Art School: Complete Character Drawing Course | UdemyDownload", "show_icon": false, "source": "sync", "type": "url", "url": "https://udemydownload.com/character-art-school/#pll_switcher", "visit_count": 0}, {"date_added": "0", "date_last_used": "0", "guid": "06921379-e803-411d-a7bc-1249840f6866", "id": "278", "name": "Coleção Completa de A Teia do Aranha - Na Teia do Homem Aranha", "show_icon": false, "source": "sync", "type": "url", "url": "http://nateiadohomemaranha.blogspot.com/2014/09/colecao-completa-de-teia-do-aranha.html", "visit_count": 0}, {"date_added": "0", "date_last_used": "0", "guid": "0bf18779-8c86-4c68-a2ee-b7046c5474a2", "id": "279", "name": "MegaHQ: <PERSON> Incrível Hulk V3", "show_icon": false, "source": "sync", "type": "url", "url": "https://megahq-online.wixsite.com/megahq/o-incrivel-hulk-v3", "visit_count": 0}, {"date_added": "0", "date_last_used": "0", "guid": "bf4fd413-c214-49c3-808f-9355f6c2a99a", "id": "280", "name": "Pacote de Cursos - Começando do Zero - CERS - (22GB) - (MEGA) - (Atualizado) ~ Cursos Torrents 2K", "show_icon": false, "source": "sync", "type": "url", "url": "https://cursostorrents.blogspot.com/2019/01/pacote-de-cursos-comecando-do-zero-cers.html#gsc.tab=0", "visit_count": 0}, {"date_added": "0", "date_last_used": "0", "guid": "aed9c73a-44db-4f53-92c1-d1f0f217ef3e", "id": "281", "name": "Aula 01 - Python - Biblioteca Face Recognition - Instalação - Código <PERSON>", "show_icon": false, "source": "sync", "type": "url", "url": "https://www.codigofluente.com.br/aula-01-python-biblioteca-face-recognition/", "visit_count": 0}, {"date_added": "0", "date_last_used": "0", "guid": "b5ba49ba-83ea-4f70-86c1-f3fd96ed0e1d", "id": "282", "name": "<PERSON><PERSON>'s Geekography", "show_icon": false, "source": "sync", "type": "url", "url": "https://exeypanteleev.com/", "visit_count": 0}, {"date_added": "0", "date_last_used": "0", "guid": "a774d14d-e02f-4cfe-a7a9-c17ed35f665b", "id": "283", "name": "Toomics - Leia comics ilimitados online", "show_icon": false, "source": "sync", "type": "url", "url": "https://toomics.com/br", "visit_count": 0}, {"date_added": "13246884439462708", "date_last_used": "0", "guid": "c9abf979-a7ed-4228-8a2e-8ceaffc57187", "id": "284", "name": "Daz to Unity Bridge | 3D Models and 3D Software by Daz 3D", "show_icon": false, "source": "sync", "type": "url", "url": "https://www.daz3d.com/daz-to-unity-bridge?utm_source=bm23&utm_medium=email&utm_term=Image+-+Daz+To+Unity&utm_content=PC%2B+Catch-Up+Weekend+Continues+-+Don%27t+Miss+It!&utm_campaign=(10-11-20)+PC%2B+Catch-Up+Weekend+Continues.++Don%27t+Miss+It", "visit_count": 0}, {"date_added": "0", "date_last_used": "0", "guid": "cbfe6efa-7f55-47ee-9626-4f144c9a1875", "id": "285", "name": "The Internet Movie Script Database (IMSDb)", "show_icon": false, "source": "sync", "type": "url", "url": "https://imsdb.com/", "visit_count": 0}, {"date_added": "0", "date_last_used": "0", "guid": "e29656c2-83ee-4bce-995d-cad293f1bcb5", "id": "286", "name": "Video-based Vital Signs Monitoring - Binah", "show_icon": false, "source": "sync", "type": "url", "url": "https://www.binah.ai/", "visit_count": 0}, {"date_added": "0", "date_last_used": "0", "guid": "eec63ba7-de7e-4cb3-8131-ac2edfe00f6e", "id": "287", "name": "Login - GenMyModel", "show_icon": false, "source": "sync", "type": "url", "url": "https://app.genmymodel.com/api/login", "visit_count": 0}, {"date_added": "0", "date_last_used": "0", "guid": "533fea2c-82ed-4850-af56-4485243eb92e", "id": "288", "name": "Zoop Dashboard", "show_icon": false, "source": "sync", "type": "url", "url": "https://dashboard.zoop.com.br/?_ga=2.161589310.360527287.1617718898-542420607.1616535121", "visit_count": 0}, {"date_added": "0", "date_last_used": "0", "guid": "86fd5815-bb07-4e19-a0e9-44147521b1a6", "id": "289", "name": "Installation Guide For Official Ender-3 V2 1.1.6V BLTouch Firmware by Creality – Smith3D", "show_icon": false, "source": "sync", "type": "url", "url": "https://smith3d.com/installation-guide-for-official-ender-3-v2-1-1-6v-bltouch-firmware/", "visit_count": 0}, {"date_added": "0", "date_last_used": "0", "guid": "9d481568-830a-43e2-9fc9-da97047e3054", "id": "290", "name": "Build Flask Apps Series - Hackers and Slackers", "show_icon": false, "source": "sync", "type": "url", "url": "https://hackersandslackers.com/series/build-flask-apps/", "visit_count": 0}, {"date_added": "0", "date_last_used": "0", "guid": "c5bcccf7-4eb8-4469-8e7d-95953e768be3", "id": "291", "name": "- SB Club", "show_icon": false, "source": "sync", "type": "url", "url": "https://sbclub.bertoluci.com.br/area/vitrine/remote/aHR0cHM6Ly9iZXJ0b2x1Y2kuY29tLmJyL3ByaW1laXJvcy1wYXNzb3Mv", "visit_count": 0}, {"date_added": "0", "date_last_used": "0", "guid": "cdb451eb-f150-4de7-89e2-04168912d3d2", "id": "292", "name": "Search CORE", "show_icon": false, "source": "sync", "type": "url", "url": "https://core.ac.uk/search?q=cacheback%20engajment&from_Year=2018&to_Year=2018", "visit_count": 0}, {"date_added": "0", "date_last_used": "0", "guid": "bad08612-ff10-4308-b519-5f1b483dac40", "id": "293", "name": "Daz to Unity Bridge | 3D Models and 3D Software by Daz 3D", "show_icon": false, "source": "sync", "type": "url", "url": "https://www.daz3d.com/daz-to-unity-bridge?utm_source=bm23&utm_medium=email&utm_term=Image+-+Daz+To+Unity&utm_content=PC%2B+Catch-Up+Weekend+Continues+-+Don%27t+Miss+It!&utm_campaign=%2810-11-20%29+PC%2B+Catch-Up+Weekend+Continues.++<PERSON>%27t+Miss+It", "visit_count": 0}], "date_added": "13274928000185153", "date_last_used": "0", "date_modified": "13274928000185153", "guid": "ebfe1a00-eba2-485d-bcf5-541f4e4456f2", "id": "270", "name": "php", "source": "unknown", "type": "folder"}, {"date_added": "0", "date_last_used": "0", "guid": "70373e27-6eb4-4df6-a8b6-04c7a2773438", "id": "294", "name": "Frame Prusa Mendel i3 Graber - GTLaser 3D - GTMax Tecnologia em Eletrônica Ltda.", "show_icon": false, "source": "sync", "type": "url", "url": "http://www.gtmax3d.com.br/produto/estrutura-frame-impressora-3d-prusa-i3-graber-em-acrilico.html", "visit_count": 0}, {"date_added": "0", "date_last_used": "0", "guid": "dfcfaf06-d987-44a7-87fe-6d63abfbb8f9", "id": "295", "name": "Apple", "show_icon": false, "source": "sync", "type": "url", "url": "http://www.apple.com/br", "visit_count": 0}, {"date_added": "0", "date_last_used": "0", "guid": "9398081c-5d09-4dcb-9522-a0295e3e9b9f", "id": "296", "name": "Disney", "show_icon": false, "source": "sync", "type": "url", "url": "http://www.disney.com.br/", "visit_count": 0}, {"date_added": "0", "date_last_used": "0", "guid": "343c21f3-b40d-467c-82fc-b319180915fc", "id": "297", "name": "ESPN", "show_icon": false, "source": "sync", "type": "url", "url": "http://www.espn.com.br/", "visit_count": 0}, {"date_added": "0", "date_last_used": "0", "guid": "e8cd9496-e349-4de8-9dda-23a2649d0cf9", "id": "298", "name": "Yahoo", "show_icon": false, "source": "sync", "type": "url", "url": "http://br.yahoo.com/", "visit_count": 0}, {"date_added": "0", "date_last_used": "0", "guid": "96667b42-9c5e-4dac-8f9e-5e4e1e6ecb74", "id": "299", "name": "Todos os aplicativos - Google Play Developer Console", "show_icon": false, "source": "sync", "type": "url", "url": "https://play.google.com/apps/publish/?dev_acc=17653610315910112002#AppListPlace", "visit_count": 0}, {"date_added": "0", "date_last_used": "0", "guid": "cd3d0cdb-e16a-4331-a0ff-8febf3abd205", "id": "300", "name": "https://play.google.com/apps/publish/?dev_acc=17653610315910112002#", "show_icon": false, "source": "sync", "type": "url", "url": "https://play.google.com/apps/publish/?dev_acc=17653610315910112002#", "visit_count": 0}, {"date_added": "0", "date_last_used": "0", "guid": "2f5f8679-b156-4e5a-91af-b09f2dc1d255", "id": "301", "name": "Entre em Microsoft Online Services", "show_icon": false, "source": "sync", "type": "url", "url": "https://login.microsoftonline.com/", "visit_count": 0}, {"date_added": "0", "date_last_used": "0", "guid": "204a616c-ab1e-47bc-ba8b-d14729f9c459", "id": "302", "name": "Bookmarks", "show_icon": false, "source": "sync", "type": "url", "url": "chrome://bookmarks/", "visit_count": 0}, {"date_added": "0", "date_last_used": "0", "guid": "43957809-e5d1-4121-9fa8-cacf4cecc7c3", "id": "303", "name": "Free Production Music by <PERSON>", "show_icon": false, "source": "sync", "type": "url", "url": "http://audionautix.com/index.php", "visit_count": 0}, {"date_added": "0", "date_last_used": "0", "guid": "d54a5161-ae32-4409-8652-130de379041b", "id": "304", "name": "RE:NEWHAVEN", "show_icon": false, "source": "sync", "type": "url", "url": "http://renewhavenct.com/contact.html", "visit_count": 0}, {"date_added": "0", "date_last_used": "0", "guid": "98d74f67-87b4-4afa-ac4a-67ebca5f9e77", "id": "305", "name": "Unreal Engine Tutorial for Beginners: Part 1", "show_icon": false, "source": "sync", "type": "url", "url": "https://www.raywenderlich.com/97058/unreal-engine-tutorial-for-beginners-part-1", "visit_count": 0}, {"date_added": "0", "date_last_used": "0", "guid": "06a04060-fbcf-46e9-a86e-e1ad0fdbf2f1", "id": "306", "name": "Onde comprar scripts, texturas, áudio e modelos 3D para jogos? - Produção de Jogos", "show_icon": false, "source": "sync", "type": "url", "url": "http://producaodejogos.com/onde-comprar-scripts-texturas-audio-e-modelos-3d-para-jogos/", "visit_count": 0}, {"date_added": "0", "date_last_used": "0", "guid": "a9a1ba91-2e8a-473d-9e53-a34190f782f2", "id": "307", "name": "CS 373: Course Content - Udacity Mirror: CS373", "show_icon": false, "source": "sync", "type": "url", "url": "https://sites.google.com/site/udacitymirrorcs373/cs-373", "visit_count": 0}, {"date_added": "0", "date_last_used": "0", "guid": "76680f4b-718e-48a3-9d49-afd1fa9924b0", "id": "308", "name": "ai-class.com 2011 archive", "show_icon": false, "source": "sync", "type": "url", "url": "https://sites.google.com/site/aiclass2011archive/home", "visit_count": 0}, {"date_added": "0", "date_last_used": "0", "guid": "6aedd2cd-24fb-47f7-85d1-5dc327b7595b", "id": "309", "name": "Stanford Engineering Everywhere | CS223A - Introduction to Robotics", "show_icon": false, "source": "sync", "type": "url", "url": "https://see.stanford.edu/Course/CS223A", "visit_count": 0}, {"date_added": "0", "date_last_used": "0", "guid": "97d336f7-fafa-4f14-acd3-394b5b991115", "id": "310", "name": "Neural Networks for Machine Learning | Coursera", "show_icon": false, "source": "sync", "type": "url", "url": "https://www.coursera.org/learn/neural-networks/home/<USER>", "visit_count": 0}, {"date_added": "0", "date_last_used": "0", "guid": "45f464a7-5171-4be1-97d6-d7b8d2b36463", "id": "311", "name": "<PERSON><PERSON><PERSON><PERSON>: self-driving <PERSON><PERSON><PERSON> with TensorF<PERSON>", "show_icon": false, "source": "sync", "type": "url", "url": "http://kevinhughes.ca/blog/tensor-kart", "visit_count": 0}, {"date_added": "0", "date_last_used": "0", "guid": "45128ad2-4020-4481-83c5-fc058481c373", "id": "312", "name": "ROCK & QUADRINHOS SCANS", "show_icon": false, "source": "sync", "type": "url", "url": "http://rockquadrinhosscans.blogspot.com.br/search?updated-max=2017-03-10T13:08:00-03:00&max-results=7", "visit_count": 0}, {"date_added": "0", "date_last_used": "0", "guid": "54bbf0a5-be1e-4f98-ab6c-c023b10e5f5f", "id": "313", "name": "ROCK & QUADRINHOS SCANS: <PERSON><PERSON><PERSON>:As 50 melhores HQS do Homem-Aranha", "show_icon": false, "source": "sync", "type": "url", "url": "http://rockquadrinhosscans.blogspot.com.br/search/label/Guedes%20Manifesto%3AAs%2050%20melhores%20HQS%20do%20Homem-<PERSON><PERSON>a", "visit_count": 0}, {"date_added": "0", "date_last_used": "0", "guid": "dec45c96-c6c1-4825-b649-831d5250e0c7", "id": "314", "name": "Strange Tales 107 ~ Marvel Clube", "show_icon": false, "source": "sync", "type": "url", "url": "http://marvelclube.blogspot.com.br/2015/03/strange-tales-107.html", "visit_count": 0}, {"date_added": "13135037960028877", "date_last_used": "0", "guid": "c2f7d147-4ad5-4505-a4b0-18b7f1358d2f", "id": "315", "name": "Udemy(1) - Documentos - sba20930 - minhateca.com.br, P<PERSON>gina 7", "show_icon": false, "source": "sync", "type": "url", "url": "http://minhateca.com.br/sba20930/Documentos/Udemy(1),7", "visit_count": 0}, {"date_added": "0", "date_last_used": "0", "guid": "5fc913e6-04bc-4b36-a4a0-6fad0b483e7b", "id": "316", "name": "VINGADORES - VÁRIOS - ## 1 - HQ's e Mangás ## - Bavi - minhateca.com.br", "show_icon": false, "source": "sync", "type": "url", "url": "http://minhateca.com.br/Bavi/*23*23+1+-+HQ*27s+e+Mang*c3*a1s+*23*23/VINGADORES+-+V*c3*81RIOS", "visit_count": 0}, {"date_added": "0", "date_last_used": "0", "guid": "aca2c944-a945-4c19-bcb8-483d1e97b8a0", "id": "317", "name": "Informática - Equipamentos - Raphael.Luiz - minhateca.com.br", "show_icon": false, "source": "sync", "type": "url", "url": "http://minhateca.com.br/Raphael.Luiz/Revit/Biblioteca+Revit+-+Custom/Equipamentos/Inform*c3*a1tica", "visit_count": 0}, {"date_added": "0", "date_last_used": "0", "guid": "e61baf64-361c-4663-82a2-e9cdb21e1daf", "id": "318", "name": "DC versus MARVEL - ## 1 - HQ's e Mangás ## - Bavi - minhateca.com.br", "show_icon": false, "source": "sync", "type": "url", "url": "http://minhateca.com.br/Bavi/*23*23+1+-+HQ*27s+e+Mang*c3*a1s+*23*23/DC+versus+MARVEL", "visit_count": 0}, {"date_added": "0", "date_last_used": "0", "guid": "fc821a86-8a1d-4a4f-b120-4cbaa7c64326", "id": "319", "name": "Packt - Learning Path - Kali Linux - Courses - CheshireKitty - minhateca.com.br", "show_icon": false, "source": "sync", "type": "url", "url": "http://minhateca.com.br/CheshireKitty/Courses/Packt+-+Learning+Path+-+Kali+Linux", "visit_count": 0}, {"date_added": "0", "date_last_used": "0", "guid": "9637d0e3-6ef5-4dec-9b36-e05d30ddbb43", "id": "320", "name": "Torrent Brazil - Filmes Via Torrent, The Pirate Brazil, Download Filmes", "show_icon": false, "source": "sync", "type": "url", "url": "http://www.torrentbrazil.org/", "visit_count": 0}, {"date_added": "0", "date_last_used": "0", "guid": "8fc3c141-5370-40c1-85bf-556afa888347", "id": "321", "name": "Deep Learning", "show_icon": false, "source": "sync", "type": "url", "url": "http://www.deeplearningbook.org/", "visit_count": 0}, {"date_added": "0", "date_last_used": "0", "guid": "44ff5c9a-af1a-4526-be35-48bac6529cbb", "id": "322", "name": "Dive into Deep Learning with 10 free online courses", "show_icon": false, "source": "sync", "type": "url", "url": "https://medium.freecodecamp.com/dive-into-deep-learning-with-these-23-online-courses-bf247d289cc0", "visit_count": 0}, {"date_added": "0", "date_last_used": "0", "guid": "a3181749-c0f4-44d0-981b-97ecdcff75eb", "id": "323", "name": "Deep Learning Summer School, Montreal 2016 - VideoLectures - VideoLectures.NET", "show_icon": false, "source": "sync", "type": "url", "url": "http://videolectures.net/deeplearning2016_montreal/", "visit_count": 0}, {"date_added": "0", "date_last_used": "0", "guid": "11f2604e-26eb-4d5d-aadc-10e0df7589ca", "id": "324", "name": "Vision VR/AR Summit 2017 – Virtual & Augmented Reality Conference", "show_icon": false, "source": "sync", "type": "url", "url": "https://visionsummit2017.com/", "visit_count": 0}, {"date_added": "0", "date_last_used": "0", "guid": "c568760f-ca4d-4f40-822a-df8c7fc85def", "id": "325", "name": "Baixar Series e Filmes De comedia - Mkv e Mp4 Torrent Bluray 720p e 1080p Dublado e Legendado Download", "show_icon": false, "source": "sync", "type": "url", "url": "http://www.comandotorrents.com/category/comedia/page/6/", "visit_count": 0}, {"date_added": "0", "date_last_used": "0", "guid": "4eb29879-a42d-4d1d-8aff-b1cd9601b827", "id": "326", "name": "Interactive presentation software - Mentimeter", "show_icon": false, "source": "sync", "type": "url", "url": "https://www.mentimeter.com/plans/education", "visit_count": 0}, {"date_added": "0", "date_last_used": "0", "guid": "75fd4408-f13f-4eb3-8f4f-2b1b071bca80", "id": "327", "name": "FTUApps.Dev | Developers' Ground", "show_icon": false, "source": "sync", "type": "url", "url": "https://ftuapps.dev/", "visit_count": 0}, {"date_added": "0", "date_last_used": "0", "guid": "71c7fdba-d8f0-4134-a617-01513ee3cbaa", "id": "328", "name": "Mindvalley Free Courses Online Free Download Torrent | FreeCoursesOnline.Me", "show_icon": false, "source": "sync", "type": "url", "url": "https://www.freecoursesonline.me/courses/mindvalley/", "visit_count": 0}, {"date_added": "0", "date_last_used": "0", "guid": "bdc09722-c3d9-4945-b9d4-1b5176f74c3f", "id": "329", "name": "Library Genesis", "show_icon": false, "source": "sync", "type": "url", "url": "http://gen.lib.rus.ec/", "visit_count": 0}, {"date_added": "0", "date_last_used": "0", "guid": "99b7375e-5fdc-41b3-b41b-6faef6175293", "id": "330", "name": "1724100E94125FFCD13D0518172FA073: free download. Ebooks library. On-line books store on Z-Library", "show_icon": false, "source": "sync", "type": "url", "url": "https://b-ok.lat/md5/1724100E94125FFCD13D0518172FA073?regionChanged=&redirect=28937455", "visit_count": 0}, {"date_added": "0", "date_last_used": "0", "guid": "af36018a-653d-44c5-9037-a12ea1edee46", "id": "331", "name": "Database CRUD Operation in Python", "show_icon": false, "source": "sync", "type": "url", "url": "https://www.tutorialsteacher.com/python/database-crud-operation-in-python", "visit_count": 0}, {"date_added": "0", "date_last_used": "0", "guid": "20199571-8b4e-4754-9957-0463cfde420f", "id": "332", "name": "crud-generator · PyPI", "show_icon": false, "source": "sync", "type": "url", "url": "https://pypi.org/project/crud-generator/", "visit_count": 0}, {"date_added": "0", "date_last_used": "0", "guid": "a6d1a422-e337-4729-8252-8f2784818faa", "id": "333", "name": "A to Z Resources for Students", "show_icon": false, "source": "sync", "type": "url", "url": "https://starwalt.in/", "visit_count": 0}, {"date_added": "0", "date_last_used": "0", "guid": "58d86207-3118-4227-ba41-6905a4a09db1", "id": "334", "name": "ArtStation - Dinamuuu 3D", "show_icon": false, "source": "sync", "type": "url", "url": "https://www.artstation.com/dinamuuu3d", "visit_count": 0}, {"date_added": "0", "date_last_used": "0", "guid": "76df0e84-a634-46ce-8819-cd02dad24d06", "id": "335", "name": "Account API - Docs - Appwrite", "show_icon": false, "source": "sync", "type": "url", "url": "https://appwrite.io/docs/client/account?sdk=flutter#create", "visit_count": 0}, {"date_added": "0", "date_last_used": "0", "guid": "a214dc48-320d-4ea2-a0cd-01e313a94033", "id": "336", "name": "JSON and serialization - Flutter", "show_icon": false, "source": "sync", "type": "url", "url": "https://flutter.dev/docs/development/data-and-backend/json", "visit_count": 0}, {"date_added": "0", "date_last_used": "0", "guid": "6bd177d9-51d8-43dc-b944-47b1ef6c2829", "id": "337", "name": "Introducing the Docker Desktop WSL 2 Backend - Docker Blog", "show_icon": false, "source": "sync", "type": "url", "url": "https://www.docker.com/blog/new-docker-desktop-wsl2-backend/", "visit_count": 0}, {"date_added": "0", "date_last_used": "0", "guid": "26ed2ea0-76fe-44c2-b524-a7f86715cba8", "id": "338", "name": "Top 12 Useful Flutter Packages that can make developers’ Life Easy : Innovatily", "show_icon": false, "source": "sync", "type": "url", "url": "https://innovatily.com/top-12-useful-flutter-packages-that-can-make-developers-life-easy/", "visit_count": 0}, {"date_added": "0", "date_last_used": "0", "guid": "faacf0c0-78b8-4006-b6e4-ab32be2008e2", "id": "339", "name": "Assinar PDF com assinatura eletrônica online grátis", "show_icon": false, "source": "sync", "type": "url", "url": "https://smallpdf.com/pt/assinar-pdf", "visit_count": 0}, {"date_added": "0", "date_last_used": "0", "guid": "bca2bfc7-a5b1-4548-a17b-d9940d3f079b", "id": "340", "name": "bizz84/layout-demo-flutter: Super Useful Flutter Layouts - Right in Your Pocket. 😉", "show_icon": false, "source": "sync", "type": "url", "url": "https://github.com/bizz84/layout-demo-flutter", "visit_count": 0}, {"date_added": "0", "date_last_used": "0", "guid": "549be7cc-ef55-49a2-9dcf-ab3ffcb1e979", "id": "341", "name": "Oracle Cloud Infrastructure | Sign In", "show_icon": false, "source": "sync", "type": "url", "url": "https://login.sa-saopaulo-1.oraclecloud.com/v1/logout?tenant=leoaalvs&post_logout_redirect_uri=https://console.sa-saopaulo-1.oraclecloud.com", "visit_count": 0}, {"date_added": "0", "date_last_used": "0", "guid": "fa0d6258-4570-4b9f-941b-2150065912ac", "id": "342", "name": "https://idcs-78a645864da74221b8e9af8fa9f385e6.identity.oraclecloud.com/fed/v1/user/response/login", "show_icon": false, "source": "sync", "type": "url", "url": "https://idcs-78a645864da74221b8e9af8fa9f385e6.identity.oraclecloud.com/fed/v1/user/response/login", "visit_count": 0}, {"date_added": "0", "date_last_used": "0", "guid": "4f672a06-e195-4541-abaa-bbb7bcb62921", "id": "343", "name": "A CHAVE DOS GRANDES MISTÉRIOS por Eli<PERSON>s Levi", "show_icon": false, "source": "sync", "type": "url", "url": "http://www.dhnet.org.br/w3/henrique/espiritualidade/salomao/eliphaslevi.htm", "visit_count": 0}, {"date_added": "0", "date_last_used": "0", "guid": "dd042454-7d7e-41c4-bd97-3f205e7aa012", "id": "344", "name": "bonfire | Flutter Package", "show_icon": false, "source": "sync", "type": "url", "url": "https://pub.dev/packages/bonfire/example", "visit_count": 0}, {"date_added": "0", "date_last_used": "0", "guid": "33a039b9-91eb-43e0-a98d-7989ee89c821", "id": "345", "name": "Python 2 vs 3", "show_icon": false, "source": "sync", "type": "url", "url": "https://devopedia.org/python-2-vs-3", "visit_count": 0}, {"date_added": "0", "date_last_used": "0", "guid": "42acbbbd-4d98-4102-a430-c131eaa90538", "id": "346", "name": "BaixaFilme.Net - Página 7 de 922 - Baixar Filme Torrent Dublado e Legendado", "show_icon": false, "source": "sync", "type": "url", "url": "https://www.baixafilme.net/page/7/?orderby=lancamento", "visit_count": 0}, {"date_added": "0", "date_last_used": "0", "guid": "c86089fa-56cf-403d-ada2-ece861de90e5", "id": "347", "name": "Search | arXiv e-print repository", "show_icon": false, "source": "sync", "type": "url", "url": "https://arxiv.org/search/?query=+gamification&searchtype=all&source=header", "visit_count": 0}, {"date_added": "0", "date_last_used": "0", "guid": "f3d97762-2bcb-495c-a09d-64f4d0455a43", "id": "348", "name": "2105.07447.pdf", "show_icon": false, "source": "sync", "type": "url", "url": "https://arxiv.org/pdf/2105.07447.pdf", "visit_count": 0}, {"date_added": "0", "date_last_used": "0", "guid": "a16970a2-6a5d-47cb-b376-92b7a20f84f4", "id": "349", "name": "Search | arXiv e-print repository", "show_icon": false, "source": "sync", "type": "url", "url": "https://arxiv.org/search/?query=teams+topologies&searchtype=all&abstracts=show&order=-announced_date_first&size=50", "visit_count": 0}, {"date_added": "0", "date_last_used": "0", "guid": "a8b38dbd-7572-4d63-9691-c1c815fa489e", "id": "350", "name": "Search | arXiv e-print repository", "show_icon": false, "source": "sync", "type": "url", "url": "https://arxiv.org/search/?query=NFT&searchtype=all&abstracts=show&order=-announced_date_first&size=50", "visit_count": 0}, {"date_added": "0", "date_last_used": "0", "guid": "4ba436f3-2215-48a2-b785-4f143741c840", "id": "351", "name": "0107 Usuário PUT - WordPress REST API - Origamid", "show_icon": false, "source": "sync", "type": "url", "url": "https://www.origamid.com/curso/wordpress-rest-api/0107-usuario-put", "visit_count": 0}, {"date_added": "0", "date_last_used": "0", "guid": "a05cbaa8-4feb-49d3-8a55-8f73f9c9a22a", "id": "352", "name": "benznest/flutter-snippet", "show_icon": false, "source": "sync", "type": "url", "url": "https://github.com/benznest/flutter-snippet", "visit_count": 0}, {"date_added": "0", "date_last_used": "0", "guid": "033ee201-c907-4f1b-a01a-2dfea38d82df", "id": "353", "name": "Portal de Associados da Amazon.com.br - Início", "show_icon": false, "source": "sync", "type": "url", "url": "https://associados.amazon.com.br/home", "visit_count": 0}, {"date_added": "0", "date_last_used": "0", "guid": "1aea7a7c-f42e-419f-9712-d928d3c3d4c0", "id": "354", "name": "Download TikTok video without watermark online - Free TikTok downloader", "show_icon": false, "source": "sync", "type": "url", "url": "https://ssstik.io/en", "visit_count": 0}, {"date_added": "0", "date_last_used": "0", "guid": "cc6688a7-5e69-40e6-a178-10ebe915ff36", "id": "355", "name": "ThisIsWhyImBroke :: The Internet's Mall", "show_icon": false, "source": "sync", "type": "url", "url": "https://www.thisiswhyimbroke.com/", "visit_count": 0}, {"date_added": "0", "date_last_used": "0", "guid": "80df0de4-cf1e-41dc-9921-7a582bdc9e63", "id": "356", "name": "logo", "show_icon": false, "source": "sync", "type": "url", "url": "https://kandi.openweaver.com/home", "visit_count": 0}, {"date_added": "0", "date_last_used": "0", "guid": "af311cab-1e9f-42c6-9e0d-f45ef0400a5d", "id": "357", "name": "get_avatar_url() | Function | WordPress Developer Resources", "show_icon": false, "source": "sync", "type": "url", "url": "https://developer.wordpress.org/reference/functions/get_avatar_url/", "visit_count": 0}, {"date_added": "0", "date_last_used": "0", "guid": "59ecbae0-9c43-44d5-817d-1f6f83d8db84", "id": "358", "name": "Mobile App Development for WordPress Website & Magazie with REST JSON API", "show_icon": false, "source": "sync", "type": "url", "url": "https://www.parorrey.com/solutions/json-api-user-plus/#https://www.parorrey.com/solutions/pi-api-documentation-wordpress-plugin/#!/prices", "visit_count": 0}, {"date_added": "0", "date_last_used": "0", "guid": "228bf8d2-f2d7-42ca-8864-6d71f4de3197", "id": "359", "name": "Flutter — Visual Studio Code Shortcuts for Fast and Efficient Development | by Ganesh .s.p | Flutter Community | Medium", "show_icon": false, "source": "sync", "type": "url", "url": "https://medium.com/flutter-community/flutter-visual-studio-code-shortcuts-for-fast-and-efficient-development-7235bc6c3b7d", "visit_count": 0}, {"date_added": "0", "date_last_used": "0", "guid": "98fe914d-afbe-429d-a597-0f2aecf9ceb7", "id": "360", "name": "Explore popular libraries | kandi", "show_icon": false, "source": "sync", "type": "url", "url": "https://kandi.openweaver.com/explore", "visit_count": 0}, {"date_added": "0", "date_last_used": "0", "guid": "1dbb7cd7-11c8-4380-869b-248f1235d1ad", "id": "361", "name": "Marketing Digital | Página 9 de 12 | Acervo Cursos", "show_icon": false, "source": "sync", "type": "url", "url": "https://acervocursos.com/category/marketing-digital/page/9/", "visit_count": 0}, {"date_added": "0", "date_last_used": "0", "guid": "aaa412e1-**************-95e00c2cc87a", "id": "362", "name": "Como lançar um aplicativo de sucesso no mercado", "show_icon": false, "source": "sync", "type": "url", "url": "https://jera.com.br/blog/5801/startups-empreendedorismo/como-lancar-um-aplicativo-de-sucesso", "visit_count": 0}, {"date_added": "0", "date_last_used": "0", "guid": "6229fc76-001f-4cf9-86be-75bca009a34e", "id": "363", "name": "Banco de Imagens Gratis, Imagens Sem Direitos Autorais & Fotos Gratuitas · Pexels", "show_icon": false, "source": "sync", "type": "url", "url": "https://www.pexels.com/pt-br/", "visit_count": 0}, {"date_added": "0", "date_last_used": "0", "guid": "cadcc1f1-8434-4f62-b5dc-bb6acaa815f6", "id": "364", "name": "FUNAPE - Portal do Coordenador", "show_icon": false, "source": "sync", "type": "url", "url": "https://funape.org.br/site/coordenador/index.php", "visit_count": 0}, {"date_added": "0", "date_last_used": "0", "guid": "30e1e631-3338-4823-9f65-b4cd0c1d1222", "id": "365", "name": "Popular models for 3D enthusiasts | Free Downloads | Thangs", "show_icon": false, "source": "sync", "type": "url", "url": "https://thangs.com/", "visit_count": 0}, {"date_added": "0", "date_last_used": "0", "guid": "4d9865e2-4e48-4f30-880e-82a2a9257bb8", "id": "366", "name": "Resoluções | UFG - Universidade Federal de Goiás", "show_icon": false, "source": "sync", "type": "url", "url": "https://www.ufg.br/n/63397-resolucoes", "visit_count": 0}, {"date_added": "0", "date_last_used": "0", "guid": "6f3cf18a-a465-40ac-8e8a-bc94483d1827", "id": "367", "name": "Self-Determination Theory | Simply Psychology", "show_icon": false, "source": "sync", "type": "url", "url": "https://www.simplypsychology.org/self-determination-theory.html", "visit_count": 0}, {"date_added": "0", "date_last_used": "0", "guid": "00b43920-413e-4f8e-b2e8-dc3e3521351b", "id": "368", "name": "iptv-revenda", "show_icon": false, "source": "sync", "type": "url", "url": "https://gestor.tv/admin/films", "visit_count": 0}, {"date_added": "0", "date_last_used": "0", "guid": "*************-4d31-8f4d-ca41f2ca3b81", "id": "369", "name": "Double Diamond — Como Utilizar essa Metodologia na Prática | by Editorial Aela.io | Aela.io | Medium", "show_icon": false, "source": "sync", "type": "url", "url": "https://medium.com/aela/double-diamond-como-utilizar-essa-metodologia-na-pr%C3%A1tica-5dc8a5d878bb", "visit_count": 0}, {"date_added": "0", "date_last_used": "0", "guid": "5ef0af9f-662f-45e8-a696-2212543f4a78", "id": "370", "name": "Development - FreeCourseSite.com - Download Udemy Paid Courses For Free", "show_icon": false, "source": "sync", "type": "url", "url": "https://www.freecoursessites.com/course/download-udemy-development-paid-courses-for-free-free-course-site/page/2/", "visit_count": 0}, {"date_added": "0", "date_last_used": "0", "guid": "d47e2c14-83d7-4228-b27e-7316f7b910cc", "id": "371", "name": "<PERSON><PERSON><PERSON><PERSON> · English in University - by Teacher <PERSON><PERSON>", "show_icon": false, "source": "sync", "type": "url", "url": "https://curso.ingles.cc/", "visit_count": 0}, {"date_added": "0", "date_last_used": "0", "guid": "4cf86e25-e098-42fc-b27a-c7d67eebda28", "id": "372", "name": "Cursos Torrent - Página 13 de 65 - <PERSON><PERSON><PERSON>", "show_icon": false, "source": "sync", "type": "url", "url": "https://www.baixartudo.online/category/cursos/page/13/", "visit_count": 0}, {"date_added": "0", "date_last_used": "0", "guid": "2ce2e6c9-017c-46f4-886e-aea234156ba8", "id": "373", "name": "Udemy(1) - Documentos - sba20930 - minhateca.com.br, P<PERSON>gina 7", "show_icon": false, "source": "sync", "type": "url", "url": "http://minhateca.com.br/sba20930/Documentos/Udemy%281%29,7", "visit_count": 0}, {"date_added": "0", "date_last_used": "0", "guid": "e109bea4-5d95-4a35-b392-367873f79662", "id": "374", "name": "Windows Torrent - Página 12 de 62 - <PERSON><PERSON><PERSON>", "show_icon": false, "source": "sync", "type": "url", "url": "https://www.baixartudo.online/category/windows/page/12/", "visit_count": 0}, {"date_added": "0", "date_last_used": "0", "guid": "50ea4c27-9b79-438d-ab81-406df7928c9a", "id": "375", "name": "Morris Method Language Programs", "show_icon": false, "source": "sync", "type": "url", "url": "https://www.mikelmorris.com/login", "visit_count": 0}, {"date_added": "0", "date_last_used": "0", "guid": "2213f4cc-1e56-4db5-8342-17a0d8dd0249", "id": "376", "name": "Curso Seus Trê<PERSON>", "show_icon": false, "source": "sync", "type": "url", "url": "https://dashboard.kiwify.com.br/course/abbd6864-f029-4116-9f90-8910df6c39b4?lesson=6eed9452-9964-4c67-85d2-edd4a29cf2dd", "visit_count": 0}, {"date_added": "0", "date_last_used": "0", "guid": "7e0fb3f8-7b04-4c8c-abcb-99e1a93dc0bd", "id": "377", "name": "Programação – Aprenda Robótica", "show_icon": false, "source": "sync", "type": "url", "url": "https://aprendarobotica.wordpress.com/category/programacao/", "visit_count": 0}, {"date_added": "13295816154640331", "date_last_used": "0", "guid": "845af0ed-f1ad-411c-a7f5-0dbff0c44e39", "id": "378", "name": "StackBlitz", "show_icon": false, "source": "sync", "type": "url", "url": "https://stackblitz.com/?starters=fullstack", "visit_count": 0}, {"date_added": "13299721366239817", "date_last_used": "0", "guid": "ffd585bc-cdad-465a-852a-6b10fbd8107c", "id": "379", "name": "Python Programming Language - GeeksforGeeks", "show_icon": false, "source": "sync", "type": "url", "url": "https://www.geeksforgeeks.org/python-programming-language/?ref=shm", "visit_count": 0}, {"date_added": "13299732416252248", "date_last_used": "0", "guid": "e942d50b-417e-4778-9e2a-49e754d68636", "id": "380", "name": "Como inserir imagem dentro de uma célula no Google Planilhas | Agência Seo Martin", "show_icon": false, "source": "sync", "type": "url", "url": "https://www.seomartin.com/como-inserir-imagem-dentro-de-uma-celula-no-google-planilhas/", "visit_count": 0}, {"date_added": "*****************", "date_last_used": "0", "guid": "b2d49ba6-4792-4600-a8ed-1501adef5cd9", "id": "381", "name": "Nova guia", "show_icon": false, "source": "sync", "type": "url", "url": "edge://newtab/", "visit_count": 0}, {"date_added": "*****************", "date_last_used": "0", "guid": "e104b429-d02f-4b52-85aa-321ca48c5348", "id": "382", "name": "Gmail", "show_icon": false, "source": "sync", "type": "url", "url": "https://accounts.google.com/b/0/AddMailService", "visit_count": 0}, {"date_added": "*****************", "date_last_used": "0", "guid": "dc70372e-5083-49de-9f2d-6714a6811c73", "id": "383", "name": "YouTube", "show_icon": false, "source": "sync", "type": "url", "url": "https://youtube.com/", "visit_count": 0}, {"date_added": "*****************", "date_last_used": "0", "guid": "028d1c5b-e018-4dc9-afe2-2b25fbea5de8", "id": "384", "name": "Maps", "show_icon": false, "source": "sync", "type": "url", "url": "https://maps.google.com/", "visit_count": 0}, {"date_added": "*****************", "date_last_used": "0", "guid": "779703a5-8878-4fee-8a26-d5ba46b02800", "id": "385", "name": "Cursive Fonts (Page 10) | FontSpace", "show_icon": false, "source": "sync", "type": "url", "url": "https://www.fontspace.com/category/cursive?p=10", "visit_count": 0}, {"date_added": "*****************", "date_last_used": "0", "guid": "f711a322-a923-49ed-9ef3-b93fbc17bf8d", "id": "386", "name": "r", "show_icon": false, "source": "sync", "type": "url", "url": "https://www.facebook.com/", "visit_count": 0}, {"date_added": "13358184886671387", "date_last_used": "0", "guid": "596c1df5-eccb-44fc-ab49-113314fb2a28", "id": "387", "meta_info": {"power_bookmark_meta": ""}, "name": "<PERSON><PERSON>", "show_icon": false, "source": "sync", "type": "url", "url": "https://padrepauloricardo.org/", "visit_count": 0}, {"date_added": "13384901267935189", "date_last_used": "0", "guid": "69454e9d-e8d7-414e-8f3f-e29331f76b33", "id": "388", "meta_info": {"power_bookmark_meta": ""}, "name": "Seitas secretas que foram renegadas pelo Cristianismo", "show_icon": false, "source": "sync", "type": "url", "url": "https://www.msn.com/pt-br/estilo-de-vida/lifestylegeneral/seitas-secretas-que-foram-renegadas-pelo-cristianismo/ss-BB1mM7mn?ocid=msedgntp&pc=DCTS&cvid=09813df173a3441dc36811122f51ccbc&ei=8#image=27", "visit_count": 1}], "date_added": "13400377491287752", "date_last_used": "0", "date_modified": "13384901267935189", "guid": "0bc5d13f-2cba-5d74-951f-3f233fe6c908", "id": "1", "name": "Barra de favoritos", "source": "unknown", "type": "folder"}, "other": {"children": [{"date_added": "13211738610000000", "date_last_used": "0", "guid": "55a202f0-40c5-4f56-9780-46676126c27c", "id": "7", "name": "(92) Artificial Intelligence & Deep Learning", "show_icon": false, "source": "sync", "type": "url", "url": "https://www.facebook.com/groups/DeepNetGroup/", "visit_count": 0}, {"children": [{"children": [{"date_added": "13208183847000000", "date_last_used": "0", "guid": "55ec8804-83be-4576-bc6e-15d7e12e5a25", "id": "10", "name": "Ajuda e tutoriais", "show_icon": false, "source": "sync", "type": "url", "url": "https://support.mozilla.org/pt-BR/products/firefox", "visit_count": 0}, {"date_added": "13208183847000000", "date_last_used": "0", "guid": "725d37d9-ecaf-4072-85e5-26a1a5569832", "id": "11", "name": "Personalizar Firefox", "show_icon": false, "source": "sync", "type": "url", "url": "https://support.mozilla.org/pt-BR/kb/customize-firefox-controls-buttons-and-toolbars?utm_source=firefox-browser&utm_medium=default-bookmarks&utm_campaign=customize", "visit_count": 0}, {"date_added": "13208183847000000", "date_last_used": "0", "guid": "b06bc1a2-183a-46f3-a7ff-4ccd81c2c39f", "id": "12", "name": "Envolva-se", "show_icon": false, "source": "sync", "type": "url", "url": "https://www.mozilla.org/pt-BR/contribute/", "visit_count": 0}, {"date_added": "13208183847000000", "date_last_used": "0", "guid": "9ac355f1-9c58-4d6f-b13e-dbdd8e16ad09", "id": "13", "name": "Quem somos", "show_icon": false, "source": "sync", "type": "url", "url": "https://www.mozilla.org/pt-BR/about/", "visit_count": 0}], "date_added": "13231900608593176", "date_last_used": "0", "date_modified": "13231900608593176", "guid": "c34b9456-905a-41fa-840a-70ff6d967cb9", "id": "9", "name": "Mozilla Firefox", "source": "unknown", "type": "folder"}], "date_added": "13231900608593120", "date_last_used": "0", "date_modified": "13231900608593120", "guid": "45a0ee59-c4e3-4027-a841-8e605a881531", "id": "8", "name": "Menu de Marcadores do Mozilla Firefox", "source": "unknown", "type": "folder"}, {"date_added": "13212415041000000", "date_last_used": "0", "guid": "a9d01c7d-0728-4582-9263-1189f9695db7", "id": "14", "name": "How to install Node.js and npm on Ubuntu 18.04 | Linuxize", "show_icon": false, "source": "sync", "type": "url", "url": "https://linuxize.com/post/how-to-install-node-js-on-ubuntu-18.04/", "visit_count": 0}, {"date_added": "13212415155000000", "date_last_used": "0", "guid": "9a0664c6-f212-4336-9ee2-dcae88ddcdd0", "id": "15", "name": "How to Install Yarn on Ubuntu 18.04 | Linuxize", "show_icon": false, "source": "sync", "type": "url", "url": "https://linuxize.com/post/how-to-install-yarn-on-ubuntu-18-04/", "visit_count": 0}, {"date_added": "13213420321000000", "date_last_used": "0", "guid": "aa8abcb7-8f48-4039-bba9-3d02ebd778d4", "id": "16", "name": "TensorFlow + Unity: How to set up a custom TensorFlow graph in Unity", "show_icon": false, "source": "sync", "type": "url", "url": "https://blog.goodaudience.com/tensorflow-unity-how-to-set-up-a-custom-tensorflow-graph-in-unity-d65cc1bd1ab1", "visit_count": 0}, {"date_added": "13214262840000000", "date_last_used": "0", "guid": "992c3bad-df4f-4fdb-a29a-fc62f467aaa7", "id": "17", "name": "Node.js Examples - Basic Examples, Module Examples, Advanced Examples", "show_icon": false, "source": "sync", "type": "url", "url": "https://www.tutorialkart.com/nodejs/node-js-examples/#Node-Example-SELECT-FROM-Table", "visit_count": 0}, {"date_added": "13219811253000000", "date_last_used": "0", "guid": "4648d749-1b0b-4caa-91cd-5260666daacf", "id": "18", "name": "Face recognition with OpenCV, Python, and deep learning - PyImageSearch", "show_icon": false, "source": "sync", "type": "url", "url": "https://www.pyimagesearch.com/2018/06/18/face-recognition-with-opencv-python-and-deep-learning/#", "visit_count": 0}, {"date_added": "13221278503000000", "date_last_used": "0", "guid": "f84a80f0-26ae-4dd7-a25a-3fd38b48a343", "id": "19", "name": "Manual | Panda3D", "show_icon": false, "source": "sync", "type": "url", "url": "https://www.panda3d.org/manual/", "visit_count": 0}, {"date_added": "13227134320000000", "date_last_used": "0", "guid": "f154baf0-374a-4f24-8799-e2bf0892a9b8", "id": "20", "name": "Online Python Code Editor to Execute Python Code", "show_icon": false, "source": "sync", "type": "url", "url": "https://pynative.com/online-python-code-editor-to-execute-python-code/", "visit_count": 0}, {"date_added": "13228165319000000", "date_last_used": "0", "guid": "6b9b8a36-6aca-4378-916b-25c415eb4e12", "id": "21", "name": "8 Best Python Cheat Sheets for Beginners & Intermediate Learners - 2019", "show_icon": false, "source": "sync", "type": "url", "url": "https://sinxloud.com/python-cheat-sheet-beginner-advanced/", "visit_count": 0}, {"date_added": "13228170667000000", "date_last_used": "0", "guid": "89890516-9d0d-4b0a-9785-c21b2b31ed8a", "id": "22", "name": "Python Cheat Sheet for Data Science: Basics – Dataquest", "show_icon": false, "source": "sync", "type": "url", "url": "https://www.dataquest.io/blog/python-cheat-sheet/", "visit_count": 0}, {"date_added": "13229909416000000", "date_last_used": "0", "guid": "e60d1622-9b7c-4a87-b84c-174db086c280", "id": "23", "name": "Baixar 72 beauty Product Promo Video Templates", "show_icon": false, "source": "sync", "type": "url", "url": "https://elements.envato.com/pt-br/video-templates/product-promo/beauty/compatible-with-after-effects/pg-2", "visit_count": 0}, {"date_added": "13230936521000000", "date_last_used": "0", "guid": "64d125b1-63e8-41a6-a003-ebbfb0147590", "id": "24", "name": "PyFormat: Using % and .format() for great good!", "show_icon": false, "source": "sync", "type": "url", "url": "https://pyformat.info/", "visit_count": 0}, {"date_added": "13231010011000000", "date_last_used": "0", "guid": "a0ef5db6-1bd1-4d4a-a9db-a9e1737307b7", "id": "25", "name": "Atermiter x99 d4 conjunto de placa mãe com xeon e5 2620 v3 LGA2011 3 cpu 2 pçs x 8 gb = 16 gb 2400 mhz ddr4 memória|Placas-mães| | - AliExpress", "show_icon": false, "source": "sync", "type": "url", "url": "https://pt.aliexpress.com/item/4000525716434.html?spm=a2g0o.detail.1000060.2.457b1f0e3oYqV3&gps-id=pcDetailBottomMoreThisSeller&scm=1007.14977.161853.0&scm_id=1007.14977.161853.0&scm-url=1007.14977.161853.0&pvid=89a734ef-aa04-4d41-b62c-05c9bc08eb5d&_t=gps-id:pcDetailBottomMoreThisSeller,scm-url:1007.14977.161853.0,pvid:89a734ef-aa04-4d41-b62c-05c9bc08eb5d,tpp_buckets:668%230%********%238_668%23808%236395%23450_668%23888%233325%2319_668%232717%237565%23708", "visit_count": 0}, {"children": [{"children": [{"date_added": "*****************", "date_last_used": "0", "guid": "9f9e897e-3ae1-451e-8ed6-3de9a26dba40", "id": "28", "name": "Conta da Microsoft | Serviços e assinaturas", "show_icon": false, "source": "sync", "type": "url", "url": "https://account.microsoft.com/services/office/install", "visit_count": 0}, {"date_added": "*****************", "date_last_used": "0", "guid": "d5687b64-9b60-4892-8edf-23c570dfabde", "id": "29", "name": "Edital_02_2019_INF.pdf", "show_icon": false, "source": "sync", "type": "url", "url": "http://inf.ufg.br/files/uploads/Edital_02_2019_INF.pdf", "visit_count": 0}, {"date_added": "*****************", "date_last_used": "0", "guid": "d504d75a-17ef-41cf-8ff3-5dc2012e5f57", "id": "30", "name": "Licenças | My Ka<PERSON>sky", "show_icon": false, "source": "sync", "type": "url", "url": "https://my.kaspersky.com/MyLicenses?startDownload=https%3A%2F%2Fmy.kaspersky.com%2FInstaller%2FTryDownload%3FproductDownloadUrl%3D%252FInstaller%252FAuthorized%253FapplicationId%253D1932%2526serviceId%253D17%2526osTypeId%253D1%2526login%253Dleoaalvs%252540gmail.com%26utm_source%3DmailShareDistributive%26utm_medium%3DDownload%25253AKTS%26utm_target%3D42674a68-8893-4c1e-a151-069bbb3e12d9%25252CBR&login=leoaalvs%40gmail.com&utm_source=mailShareDistributive&utm_medium=Download%253AKTS&utm_target=42674a68-8893-4c1e-a151-069bbb3e12d9%252CBR#/", "visit_count": 0}], "date_added": "13231900603127856", "date_last_used": "0", "date_modified": "13231900603127856", "guid": "4b9c1f0a-7308-44d6-ad35-c2cd83829923", "id": "27", "name": "Grupo 1", "source": "unknown", "type": "folder"}, {"children": [{"date_added": "13201846052797412", "date_last_used": "0", "guid": "65507ba2-84ee-4e8b-9ff3-3bf832164518", "id": "32", "name": "Link para o download do Kaspersky Total Security - <EMAIL> - Gmail", "show_icon": false, "source": "sync", "type": "url", "url": "https://mail.google.com/mail/u/0/#search/kaspers/FMfcgxwBWKZzwPzPZcPlftQXjRdxPGKq", "visit_count": 0}], "date_added": "13231900603128280", "date_last_used": "0", "date_modified": "13231900603128280", "guid": "1b490f70-039c-4455-a041-19de95a46e32", "id": "31", "name": "Grupo 2", "source": "unknown", "type": "folder"}], "date_added": "13231900603127580", "date_last_used": "0", "date_modified": "13231900603127580", "guid": "3b034ae7-0c2d-4687-8034-f1e1244f598c", "id": "26", "name": "<PERSON><PERSON><PERSON> reservadas", "source": "unknown", "type": "folder"}, {"children": [{"date_added": "13053965705755207", "date_last_used": "0", "guid": "df964a24-08e7-4b9f-9cc1-4307ce47e665", "id": "34", "name": "Dell", "show_icon": false, "source": "sync", "type": "url", "url": "javascript:void(0)", "visit_count": 0}, {"date_added": "13052779380574156", "date_last_used": "0", "guid": "c4c295ad-2d8c-47da-bbda-387f55018bb6", "id": "35", "name": "Dell", "show_icon": false, "source": "sync", "type": "url", "url": "http://www1.la.dell.com/content/default.aspx?c=br&l=pt&s=gen", "visit_count": 0}, {"date_added": "13052779380583156", "date_last_used": "0", "guid": "6a209966-3719-4fa5-b161-46fc4425b283", "id": "36", "name": "Support.Dell.Com", "show_icon": false, "source": "sync", "type": "url", "url": "http://support.dell.com/support/index.aspx?c=br&l=pt&s=gen", "visit_count": 0}], "date_added": "13274928000173620", "date_last_used": "0", "date_modified": "13274928000173620", "guid": "8ea33e95-3b53-487d-bb5d-7e02df648167", "id": "33", "name": "Dell", "source": "unknown", "type": "folder"}, {"date_added": "13052779380511156", "date_last_used": "0", "guid": "a28edd01-a9fa-43a8-8906-2a934604e928", "id": "37", "name": "<PERSON>", "show_icon": false, "source": "sync", "type": "url", "url": "http://www.bing.com/?pc=APPM", "visit_count": 0}, {"children": [{"date_added": "13052779380592156", "date_last_used": "0", "guid": "2c143882-743f-419a-bbd2-20b094a7b003", "id": "39", "name": "Dicas do Windows Phone", "show_icon": false, "source": "sync", "type": "url", "url": "http://go.microsoft.com/fwlink/?LinkId=247436", "visit_count": 0}, {"date_added": "13052779380602156", "date_last_used": "0", "guid": "4f80a359-bad6-4b97-971e-48922040a03a", "id": "40", "name": "Nokia Support", "show_icon": false, "source": "sync", "type": "url", "url": "http://link.nokia.com/entry/van/nsupport/925", "visit_count": 0}, {"date_added": "13052779380611156", "date_last_used": "0", "guid": "d86ec897-f5d1-4ae5-9335-9f09ea1b0e66", "id": "41", "name": "Nokia.com", "show_icon": false, "source": "sync", "type": "url", "url": "http://nokia.mobi/entry/van/main/925", "visit_count": 0}, {"date_added": "13052779380621156", "date_last_used": "0", "guid": "93cd4324-3af9-4f13-a86d-392c2c958f23", "id": "42", "name": "Site Vivo", "show_icon": false, "source": "sync", "type": "url", "url": "http://www.vivo.com.br/conteudosmartphone", "visit_count": 0}, {"date_added": "13052779380630156", "date_last_used": "0", "guid": "dfbaace7-**************-06ca6eb2e4fa", "id": "43", "name": "Vivo Busca", "show_icon": false, "source": "sync", "type": "url", "url": "http://www.google.com.br/m/search?client=ms-hms-tef-br", "visit_count": 0}], "date_added": "13274928000173802", "date_last_used": "0", "date_modified": "13274928000173802", "guid": "97c26c04-369c-49d6-af3f-4c820ea9ba46", "id": "38", "name": "Telefone", "source": "unknown", "type": "folder"}, {"children": [{"date_added": "13052779380639156", "date_last_used": "0", "guid": "f1cc380c-510d-4db1-a82b-a0c64fdaf720", "id": "45", "name": "Ajuda e dicas", "show_icon": false, "source": "sync", "type": "url", "url": "https://www.mozilla.org/pt-BR/firefox/help/", "visit_count": 0}, {"date_added": "13052779380647156", "date_last_used": "0", "guid": "f1c91e6e-0a3f-40c3-a56e-64c046fb87f2", "id": "46", "name": "Personalize o Firefox", "show_icon": false, "source": "sync", "type": "url", "url": "https://www.mozilla.org/pt-BR/firefox/customize/", "visit_count": 0}, {"date_added": "13052779380658156", "date_last_used": "0", "guid": "08cb6d82-89d9-4650-8750-de94e86ad291", "id": "47", "name": "Participe", "show_icon": false, "source": "sync", "type": "url", "url": "https://www.mozilla.org/pt-BR/contribute/", "visit_count": 0}, {"date_added": "13052779380669156", "date_last_used": "0", "guid": "51a4d663-5a99-40c1-9b45-c7469e596546", "id": "48", "name": "Sobre nós", "show_icon": false, "source": "sync", "type": "url", "url": "https://www.mozilla.org/pt-BR/about/", "visit_count": 0}], "date_added": "13274928000174094", "date_last_used": "0", "date_modified": "13274928000174094", "guid": "b27ab507-510f-4972-81e3-ea7deb0038cb", "id": "44", "name": "Mozilla Firefox", "source": "unknown", "type": "folder"}, {"date_added": "13052779380330000", "date_last_used": "0", "guid": "e9f9f24a-86ef-4b88-b914-8c4a02542027", "id": "49", "name": "Google", "show_icon": false, "source": "sync", "type": "url", "url": "http://www.google.com.br/", "visit_count": 0}, {"date_added": "13052779380556156", "date_last_used": "0", "guid": "98bfb639-701d-4e99-8ecb-f5f9a3f092f7", "id": "50", "name": "<PERSON>", "show_icon": false, "source": "sync", "type": "url", "url": "http://go.microsoft.com/fwlink/p/?LinkId=255142", "visit_count": 0}, {"date_added": "13052779380330000", "date_last_used": "0", "guid": "7f5bbbea-edbd-4e2a-a518-00845db6ff17", "id": "51", "name": "Entrada (14.161) - <EMAIL> - Gmail", "show_icon": false, "source": "sync", "type": "url", "url": "https://mail.google.com/mail/u/0/#inbox?compose=new", "visit_count": 0}, {"date_added": "13104735462474905", "date_last_used": "0", "guid": "aed7ea0c-ab33-49d8-b9cd-bb9ea5fcb7f3", "id": "52", "name": "<PERSON> - Livros de Mobile | Livro Android, iOS, Web Services, Computação em Nuvem | Page 2", "show_icon": false, "source": "sync", "type": "url", "url": "http://ricardolecheta.com.br/?paged=2", "visit_count": 0}, {"date_added": "13104735468131078", "date_last_used": "0", "guid": "7010a1e3-2b5a-4c80-b820-05363ba8597f", "id": "53", "name": "GitHub - rlecheta/IntelSoftwareDay2015: Projeto exemplo Material Design Support Library", "show_icon": false, "source": "sync", "type": "url", "url": "https://github.com/rlecheta/IntelSoftwareDay2015", "visit_count": 0}, {"date_added": "13104735473785464", "date_last_used": "0", "guid": "64b15055-f466-4455-9375-60b8a6bfdb9b", "id": "54", "name": "Unity - Scripting API: Cursor.SetCursor(Texture2D,CursorMode)", "show_icon": false, "source": "sync", "type": "url", "url": "http://docs.unity3d.com/ScriptReference/Cursor.SetCursor.html", "visit_count": 0}, {"date_added": "13141059128042039", "date_last_used": "0", "guid": "1f351fb6-ef05-4489-93ee-9a5c92cbc631", "id": "55", "name": "Gamers Insights | Newzoo", "show_icon": false, "source": "sync", "type": "url", "url": "https://newzoo.com/insights/tags/gamers/", "visit_count": 0}, {"date_added": "13144072442703126", "date_last_used": "0", "guid": "c6fdcbf4-c526-4b19-b1de-8f4549b79549", "id": "56", "name": "Download Filme Mulher Maravilha Dublado Torrent HD 1080p 720p | Filmes para Download - Baixar Filmes Torrent Completo HD Melhor Qualidade", "show_icon": false, "source": "sync", "type": "url", "url": "http://www.filmesparadownload.club/mulher-maravilha-dublado-torrent-hd/", "visit_count": 0}, {"date_added": "13146497844253047", "date_last_used": "0", "guid": "e19324ea-951f-4ac0-a340-b9f5859ec4c6", "id": "57", "name": "The 8 best VR porn sites on the internet", "show_icon": false, "source": "sync", "type": "url", "url": "https://www.dailydot.com/irl/best-vr-porn-sites/", "visit_count": 0}, {"date_added": "13146498419645718", "date_last_used": "0", "guid": "5018bf42-c5e3-4607-baf3-1f78fc9a6697", "id": "58", "name": "Ariadna - VirtualRealPorn.com", "show_icon": false, "source": "sync", "type": "url", "url": "https://virtualrealporn.com/model/ariadna/", "visit_count": 0}, {"date_added": "13146498432845088", "date_last_used": "0", "guid": "27f99aca-2f7d-4ad2-b149-c192d71bfa27", "id": "59", "name": "Pornstars - VirtualRealPorn.com", "show_icon": false, "source": "sync", "type": "url", "url": "https://virtualrealporn.com/pornstars/", "visit_count": 0}, {"date_added": "13152545548233327", "date_last_used": "0", "guid": "c5fc5bdc-4b4b-4872-8ffc-2a5b9dd47e21", "id": "60", "name": "[ET] Atômica TORRENT (2017) (Atomic Blonde) TS", "show_icon": false, "source": "sync", "type": "url", "url": "https://etztorrents.org/atomica-torrent-legendado-e-dublado/", "visit_count": 0}, {"children": [{"date_added": "13208301982916179", "date_last_used": "0", "guid": "0daba090-3139-4aa3-aa72-2756334de9ab", "id": "62", "name": "Acesse sua conta na Amazon.com.br", "show_icon": false, "source": "sync", "type": "url", "url": "https://www.amazon.com/ap/signin?openid.assoc_handle=aws&openid.return_to=https%3A%2F%2Fsignin.aws.amazon.com%2Foauth%3Fcoupled_root%3Dtrue%26response_type%3Dcode%26redirect_uri%3Dhttps%253A%252F%252Fconsole.aws.amazon.com%252Fec2%252Fv2%252Fhome%253Fregion%253Dus-east-1%2526state%253DhashArgs%252523Instances%25253Asort%25253DinstanceId%2526isauthcode%253Dtrue%26client_id%3Darn%253Aaws%253Aiam%253A%253A015428540659%253Auser%252Fec2&openid.mode=checkid_setup&openid.ns=http%3A%2F%2Fspecs.openid.net%2Fauth%2F2.0&openid.identity=http%3A%2F%2Fspecs.openid.net%2Fauth%2F2.0%2Fidentifier_select&openid.claimed_id=http%3A%2F%2Fspecs.openid.net%2Fauth%2F2.0%2Fidentifier_select&action=&disableCorpSignUp=&clientContext=&marketPlaceId=&poolName=&authCookies=&pageId=aws.login&siteState=registered%2CPT_BR&accountStatusPolicy=P1&sso=&openid.pape.preferred_auth_policies=MultifactorPhysical&openid.pape.max_auth_age=120&openid.ns.pape=http%3A%2F%2Fspecs.openid.net%2Fextensions%2Fpape%2F1.0&server=%2Fap%2Fsignin%3Fie%3DUTF8&accountPoolAlias=&forceMobileApp=0&language=PT_BR&forceMobileLayout=0&awsEmail=leoaalvs%40gmail.com", "visit_count": 0}, {"date_added": "*****************", "date_last_used": "0", "guid": "852e1070-06f8-4078-bc68-6a476ee32536", "id": "63", "name": "AWS Servers - Google Drive", "show_icon": false, "source": "sync", "type": "url", "url": "https://drive.google.com/drive/folders/19LCjpyomizqUl5CGO8lQsLkHiFtRjMQf", "visit_count": 0}, {"date_added": "*****************", "date_last_used": "0", "guid": "1035cb84-68c9-47d6-b331-fa6090570e8b", "id": "64", "name": "www.udemy.com", "show_icon": false, "source": "sync", "type": "url", "url": "https://www.udemy.com/rest-apis-development-with-loopback/learn/lecture/9442174#overview", "visit_count": 0}, {"date_added": "*****************", "date_last_used": "0", "guid": "0cdfbb6a-b6e7-42ef-8bb9-5a39fb8781b6", "id": "65", "name": "www.udemy.com", "show_icon": false, "source": "sync", "type": "url", "url": "https://www.udemy.com/courses/search/?q=sails%20js&src=sac&kw=sails", "visit_count": 0}, {"date_added": "13208301982929879", "date_last_used": "0", "guid": "485851b0-a929-4c0c-8569-515f2297e706", "id": "66", "name": "LoopBack API Explorer", "show_icon": false, "source": "sync", "type": "url", "url": "http://3.220.20.62:3000/explorer/#!/Message/Message_greet", "visit_count": 0}, {"date_added": "13208301982935073", "date_last_used": "0", "guid": "80b0f419-36a5-4884-8d32-a12650a561c1", "id": "67", "name": "Search results for: 'loopback'", "show_icon": false, "source": "sync", "type": "url", "url": "https://www.packtpub.com/catalogsearch/result/?q=loopback", "visit_count": 0}, {"date_added": "13208301982937094", "date_last_used": "0", "guid": "f937e9d0-5e11-48f5-bb9a-624f88e57880", "id": "68", "name": "loopback - <PERSON><PERSON><PERSON>", "show_icon": false, "source": "sync", "type": "url", "url": "https://www.lynda.com/search?q=loopback", "visit_count": 0}, {"date_added": "13208301982944446", "date_last_used": "0", "guid": "371e82f8-13fe-4b32-8440-a2b03cc1a2a1", "id": "69", "name": "Building and running ML models for data scientists", "show_icon": false, "source": "sync", "type": "url", "url": "https://www.lynda.com/Data-Science-tutorials/Building-running-ML-models-data-scientists/711823/775356-4.html?srchtrk=index%3a30%0alinktypeid%3a2%0aq%3adocker%0apage%3a1%0as%3arelevance%0asa%3atrue%0aproducttypeid%3a2", "visit_count": 0}, {"date_added": "13208301982944920", "date_last_used": "0", "guid": "cc1311f9-84be-4dd2-83d5-1d6b17521eb8", "id": "70", "name": "mesg.ebay.com", "show_icon": false, "source": "sync", "type": "url", "url": "https://mesg.ebay.com/mesgweb/ViewMessageDetail/0/All/107793309993", "visit_count": 0}, {"date_added": "13208301982948298", "date_last_used": "0", "guid": "8fbdb5e8-8b7a-4a3b-b47a-810e9185b302", "id": "71", "name": "mysql - <PERSON><PERSON>", "show_icon": false, "source": "sync", "type": "url", "url": "https://hub.docker.com/_/mysql", "visit_count": 0}, {"date_added": "13208301982949880", "date_last_used": "0", "guid": "7d1c74bd-fae0-4825-a349-60354cfcd51b", "id": "72", "name": "mail.google.com", "show_icon": false, "source": "sync", "type": "url", "url": "https://mail.google.com/mail/u/0/#inbox/********************************", "visit_count": 0}, {"date_added": "13208301982954112", "date_last_used": "0", "guid": "a66adf9b-6506-402d-95cc-9c54bab8b29e", "id": "73", "name": "phpmyadmin/phpmyadmin - <PERSON><PERSON>", "show_icon": false, "source": "sync", "type": "url", "url": "https://hub.docker.com/r/phpmyadmin/phpmyadmin/", "visit_count": 0}, {"date_added": "13208301982956305", "date_last_used": "0", "guid": "1c7cfdbc-9e14-4cc4-b098-dbadcfbf3771", "id": "74", "name": "Implementing auto-migration | LoopBack Documentation", "show_icon": false, "source": "sync", "type": "url", "url": "https://loopback.io/doc/en/lb3/Implementing-auto-migration.html", "visit_count": 0}, {"date_added": "13208301982958612", "date_last_used": "0", "guid": "b7240a63-9682-4245-bdda-7961ddb7021a", "id": "75", "name": "3.220.20.62:3000", "show_icon": false, "source": "sync", "type": "url", "url": "http://3.220.20.62:3000/", "visit_count": 0}, {"date_added": "13208301982963656", "date_last_used": "0", "guid": "3475ccd9-0820-4d2f-ab24-f05e760fc289", "id": "76", "name": "Orange Pi PC ArcadeRetro1.1 imagem 32GB RetrOrangepi 4.2 Full + Kodi + GPIO", "show_icon": false, "source": "sync", "type": "url", "url": "http://www.orangepiretro.tk/2019/01/orange-pi-pc-arcaderetro11-imagem-32gb.html", "visit_count": 0}, {"date_added": "13208301982965429", "date_last_used": "0", "guid": "5ba2bc20-4608-4dcb-b499-23a2972a9492", "id": "77", "name": "Connecting to MySQL Database using NodeJS", "show_icon": false, "source": "sync", "type": "url", "url": "https://o7planning.org/en/11959/connecting-to-mysql-database-using-nodejs", "visit_count": 0}, {"date_added": "13208301982966853", "date_last_used": "0", "guid": "f1905e8a-0fe0-49e6-8ee9-fccda4b5dfa4", "id": "78", "name": "tutum/mongodb Tags - <PERSON><PERSON>", "show_icon": false, "source": "sync", "type": "url", "url": "https://hub.docker.com/r/tutum/mongodb/tags", "visit_count": 0}, {"date_added": "13208301982968580", "date_last_used": "0", "guid": "bf7a32c9-3ccd-42b1-a28f-6a56f82c253b", "id": "79", "name": "Docker: <PERSON><PERSON><PERSON> servid<PERSON> - dockerbr - Medium", "show_icon": false, "source": "sync", "type": "url", "url": "https://medium.com/dockerbr/mongodb-no-docker-dd3b72c7efb7", "visit_count": 0}, {"date_added": "13208301982969992", "date_last_used": "0", "guid": "bde27a7d-1b88-45ca-b052-95ad285b5340", "id": "80", "name": "mongod 68f2b48c5ffb", "show_icon": false, "source": "sync", "type": "url", "url": "http://3.220.20.62:28017/", "visit_count": 0}, {"date_added": "13208301982971476", "date_last_used": "0", "guid": "ce0a2a66-d4d9-4b8a-8fe7-c8995353e666", "id": "81", "name": "www.udemy.com", "show_icon": false, "source": "sync", "type": "url", "url": "https://www.udemy.com/courses/search/?q=loopback%20nodejs&src=sac&kw=loopback&p=2", "visit_count": 0}, {"date_added": "13208301982973930", "date_last_used": "0", "guid": "700a03df-613e-4527-af8a-f7dab0a21d33", "id": "82", "name": "www.udemy.com", "show_icon": false, "source": "sync", "type": "url", "url": "https://www.udemy.com/unity-rest-api-node-js-banco-de-dados-online/", "visit_count": 0}, {"date_added": "13208301982976092", "date_last_used": "0", "guid": "3a2b0b6a-053b-46a4-996b-33a9c540284b", "id": "83", "name": "14 Best NodeJS Frameworks for Developers in 2019", "show_icon": false, "source": "sync", "type": "url", "url": "https://www.tecmint.com/best-nodejs-frameworks-for-developers/", "visit_count": 0}, {"date_added": "13208301982977412", "date_last_used": "0", "guid": "979341be-7a98-4232-baef-b8e4e6044764", "id": "84", "name": "web.whatsapp.com", "show_icon": false, "source": "sync", "type": "url", "url": "https://web.whatsapp.com/", "visit_count": 0}, {"date_added": "13208301982979506", "date_last_used": "0", "guid": "d592c8fb-7eaa-485e-8919-fd8ed2d6dceb", "id": "85", "name": "www.youtube.com", "show_icon": false, "source": "sync", "type": "url", "url": "https://www.youtube.com/watch?v=UwBj3Xw7wmw&list=PLYO9T4zW_XsnN9dqUm76GY-Hr2TfS7Z8h&index=5", "visit_count": 0}, {"date_added": "13208301982981884", "date_last_used": "0", "guid": "c73fe8b1-c6b6-48bb-971e-c351e0a6885f", "id": "86", "name": "3.220.20.62", "show_icon": false, "source": "sync", "type": "url", "url": "http://3.220.20.62:3000/", "visit_count": 0}, {"date_added": "13208301982987352", "date_last_used": "0", "guid": "12c8dd83-615a-4782-9e9a-b524604e0c64", "id": "87", "name": "(271) How to Use a GUI with Ubuntu Linux on AWS EC2 - YouTube", "show_icon": false, "source": "sync", "type": "url", "url": "https://www.youtube.com/watch?v=6x_okhl_CF4", "visit_count": 0}, {"date_added": "13208545764028388", "date_last_used": "0", "guid": "a4a80047-a6de-42ea-942f-2c9d447c83d4", "id": "88", "name": "Best FREE Unity Assets - Over 200 Quality Curated Assets for Unity 3D", "show_icon": false, "source": "sync", "type": "url", "url": "http://www.procedural-worlds.com/blog/best-free-unity-assets-categorised-mega-list/", "visit_count": 0}], "date_added": "13274928000174692", "date_last_used": "0", "date_modified": "13274928000174692", "guid": "4c423e11-51af-43f3-b538-ffdfc48dac81", "id": "61", "name": "temporary Panic", "source": "unknown", "type": "folder"}, {"children": [], "date_added": "13294812331214942", "date_last_used": "0", "date_modified": "13294812331214942", "guid": "c9cc9657-06ba-4de3-b333-52dd02dbb8a3", "id": "89", "name": "Bookmark Bar", "source": "unknown", "type": "folder"}, {"children": [{"children": [{"date_added": "0", "date_last_used": "0", "guid": "5486faa8-f978-4f9e-8a48-4c4c0f2ef6c0", "id": "92", "name": "Dell", "show_icon": false, "source": "sync", "type": "url", "url": "http://www1.la.dell.com/content/default.aspx?c=br&l=pt&s=gen", "visit_count": 0}, {"date_added": "0", "date_last_used": "0", "guid": "28f020cd-6ccd-4aa5-b50b-6719d1343d44", "id": "93", "name": "Support.Dell.Com", "show_icon": false, "source": "sync", "type": "url", "url": "http://support.dell.com/support/index.aspx?c=br&l=pt&s=gen", "visit_count": 0}], "date_added": "13294812331215113", "date_last_used": "0", "date_modified": "13294812331215113", "guid": "3fd7ebc1-e204-40d7-88a4-f94edf80a449", "id": "91", "name": "Dell", "source": "unknown", "type": "folder"}, {"date_added": "0", "date_last_used": "0", "guid": "589b43af-bab0-47d0-95c2-3c3183034a57", "id": "94", "name": "<PERSON>", "show_icon": false, "source": "sync", "type": "url", "url": "http://www.bing.com/?pc=APPM", "visit_count": 0}, {"children": [{"date_added": "0", "date_last_used": "0", "guid": "68a21e40-31bd-4cfc-a354-54ea86019007", "id": "96", "name": "Dicas do Windows Phone", "show_icon": false, "source": "sync", "type": "url", "url": "http://go.microsoft.com/fwlink/?LinkId=247436", "visit_count": 0}, {"date_added": "0", "date_last_used": "0", "guid": "19b2a19e-3df7-4a40-a595-68d02807aeaa", "id": "97", "name": "Nokia Support", "show_icon": false, "source": "sync", "type": "url", "url": "http://link.nokia.com/entry/van/nsupport/925", "visit_count": 0}, {"date_added": "0", "date_last_used": "0", "guid": "920a3526-d8f5-45d6-9829-05f1dc8ac60d", "id": "98", "name": "Nokia.com", "show_icon": false, "source": "sync", "type": "url", "url": "http://nokia.mobi/entry/van/main/925", "visit_count": 0}, {"date_added": "0", "date_last_used": "0", "guid": "454fed51-0227-4560-ba27-9de959fb6bf8", "id": "99", "name": "Site Vivo", "show_icon": false, "source": "sync", "type": "url", "url": "http://www.vivo.com.br/conteudosmartphone", "visit_count": 0}, {"date_added": "0", "date_last_used": "0", "guid": "2ff7c7ab-ee12-4aa8-b674-516daf5a6033", "id": "100", "name": "Vivo Busca", "show_icon": false, "source": "sync", "type": "url", "url": "http://www.google.com.br/m/search?client=ms-hms-tef-br", "visit_count": 0}], "date_added": "13294812331215210", "date_last_used": "0", "date_modified": "13294812331215210", "guid": "2e22d1da-8743-42e6-a702-103fd7f86e2e", "id": "95", "name": "Telefone", "source": "unknown", "type": "folder"}, {"children": [{"date_added": "0", "date_last_used": "0", "guid": "199313a1-609d-4165-adf8-f4f3b8a3a6c6", "id": "102", "name": "Ajuda e dicas", "show_icon": false, "source": "sync", "type": "url", "url": "https://www.mozilla.org/pt-BR/firefox/help/", "visit_count": 0}, {"date_added": "0", "date_last_used": "0", "guid": "5b7c97ae-9409-459a-a51e-94f9486afe05", "id": "103", "name": "Personalize o Firefox", "show_icon": false, "source": "sync", "type": "url", "url": "https://www.mozilla.org/pt-BR/firefox/customize/", "visit_count": 0}, {"date_added": "0", "date_last_used": "0", "guid": "544d0a5c-0941-4f38-9b13-ccae5b6c07e8", "id": "104", "name": "Participe", "show_icon": false, "source": "sync", "type": "url", "url": "https://www.mozilla.org/pt-BR/contribute/", "visit_count": 0}, {"date_added": "0", "date_last_used": "0", "guid": "bffa6900-7e71-41a4-8d8b-7a43b3004e88", "id": "105", "name": "Sobre nós", "show_icon": false, "source": "sync", "type": "url", "url": "https://www.mozilla.org/pt-BR/about/", "visit_count": 0}], "date_added": "13294812331215356", "date_last_used": "0", "date_modified": "13294812331215356", "guid": "ef687bf7-6aef-4b54-b6a2-610e756c0e6c", "id": "101", "name": "Mozilla Firefox", "source": "unknown", "type": "folder"}, {"date_added": "0", "date_last_used": "0", "guid": "72b5cb3d-b8c7-4729-ba4f-32c3bffec9c2", "id": "106", "name": "Google", "show_icon": false, "source": "sync", "type": "url", "url": "http://www.google.com.br/", "visit_count": 0}, {"date_added": "0", "date_last_used": "0", "guid": "8e4d9eeb-f5cc-44e7-8169-e24bb453e03d", "id": "107", "name": "<PERSON>", "show_icon": false, "source": "sync", "type": "url", "url": "http://go.microsoft.com/fwlink/p/?LinkId=255142", "visit_count": 0}, {"date_added": "0", "date_last_used": "0", "guid": "0d86db4e-3db0-4227-8c16-6cafe19a99ab", "id": "108", "name": "Entrada (14.161) - <EMAIL> - Gmail", "show_icon": false, "source": "sync", "type": "url", "url": "https://mail.google.com/mail/u/0/#inbox?compose=new", "visit_count": 0}, {"date_added": "0", "date_last_used": "0", "guid": "e2799fbd-5f47-4c42-b387-cd256095c4eb", "id": "109", "name": "<PERSON> - Livros de Mobile | Livro Android, iOS, Web Services, Computação em Nuvem | Page 2", "show_icon": false, "source": "sync", "type": "url", "url": "http://ricardolecheta.com.br/?paged=2", "visit_count": 0}, {"date_added": "0", "date_last_used": "0", "guid": "7744100f-2eeb-4c18-9fda-745515e13aea", "id": "110", "name": "GitHub - rlecheta/IntelSoftwareDay2015: Projeto exemplo Material Design Support Library", "show_icon": false, "source": "sync", "type": "url", "url": "https://github.com/rlecheta/IntelSoftwareDay2015", "visit_count": 0}, {"date_added": "0", "date_last_used": "0", "guid": "d5d393b1-620d-405f-ba6a-65572e5f86ec", "id": "111", "name": "Unity - Scripting API: Cursor.SetCursor(Texture2D,CursorMode)", "show_icon": false, "source": "sync", "type": "url", "url": "http://docs.unity3d.com/ScriptReference/Cursor.SetCursor.html", "visit_count": 0}, {"date_added": "0", "date_last_used": "0", "guid": "81b3b900-214e-4085-b5cd-8375a1055bec", "id": "112", "name": "Gamers Insights | Newzoo", "show_icon": false, "source": "sync", "type": "url", "url": "https://newzoo.com/insights/tags/gamers/", "visit_count": 0}, {"date_added": "0", "date_last_used": "0", "guid": "f663d828-52a9-4937-854c-bebce7dd7ca4", "id": "113", "name": "Download Filme Mulher Maravilha Dublado Torrent HD 1080p 720p | Filmes para Download - Baixar Filmes Torrent Completo HD Melhor Qualidade", "show_icon": false, "source": "sync", "type": "url", "url": "http://www.filmesparadownload.club/mulher-maravilha-dublado-torrent-hd/", "visit_count": 0}, {"date_added": "0", "date_last_used": "0", "guid": "cdee4a01-b175-4fdf-aad8-2392c858ac43", "id": "114", "name": "The 8 best VR porn sites on the internet", "show_icon": false, "source": "sync", "type": "url", "url": "https://www.dailydot.com/irl/best-vr-porn-sites/", "visit_count": 0}, {"date_added": "0", "date_last_used": "0", "guid": "80c629ae-964d-4b53-a37e-8ca5a3bed3df", "id": "115", "name": "Ariadna - VirtualRealPorn.com", "show_icon": false, "source": "sync", "type": "url", "url": "https://virtualrealporn.com/model/ariadna/", "visit_count": 0}, {"date_added": "0", "date_last_used": "0", "guid": "ec03316f-6d08-420b-b9ea-de3683cf3f8a", "id": "116", "name": "Pornstars - VirtualRealPorn.com", "show_icon": false, "source": "sync", "type": "url", "url": "https://virtualrealporn.com/pornstars/", "visit_count": 0}, {"date_added": "0", "date_last_used": "0", "guid": "2a3a29bb-4b85-4040-b2fb-8a8059cb0b8f", "id": "117", "name": "[ET] Atômica TORRENT (2017) (Atomic Blonde) TS", "show_icon": false, "source": "sync", "type": "url", "url": "https://etztorrents.org/atomica-torrent-legendado-e-dublado/", "visit_count": 0}, {"children": [{"date_added": "0", "date_last_used": "0", "guid": "8bcfe5bd-492f-4160-9e48-cb0859b2d2c0", "id": "119", "name": "Acesse sua conta na Amazon.com.br", "show_icon": false, "source": "sync", "type": "url", "url": "https://www.amazon.com/ap/signin?openid.assoc_handle=aws&openid.return_to=https%3A%2F%2Fsignin.aws.amazon.com%2Foauth%3Fcoupled_root%3Dtrue%26response_type%3Dcode%26redirect_uri%3Dhttps%253A%252F%252Fconsole.aws.amazon.com%252Fec2%252Fv2%252Fhome%253Fregion%253Dus-east-1%2526state%253DhashArgs%252523Instances%25253Asort%25253DinstanceId%2526isauthcode%253Dtrue%26client_id%3Darn%253Aaws%253Aiam%253A%253A015428540659%253Auser%252Fec2&openid.mode=checkid_setup&openid.ns=http%3A%2F%2Fspecs.openid.net%2Fauth%2F2.0&openid.identity=http%3A%2F%2Fspecs.openid.net%2Fauth%2F2.0%2Fidentifier_select&openid.claimed_id=http%3A%2F%2Fspecs.openid.net%2Fauth%2F2.0%2Fidentifier_select&action=&disableCorpSignUp=&clientContext=&marketPlaceId=&poolName=&authCookies=&pageId=aws.login&siteState=registered%2CPT_BR&accountStatusPolicy=P1&sso=&openid.pape.preferred_auth_policies=MultifactorPhysical&openid.pape.max_auth_age=120&openid.ns.pape=http%3A%2F%2Fspecs.openid.net%2Fextensions%2Fpape%2F1.0&server=%2Fap%2Fsignin%3Fie%3DUTF8&accountPoolAlias=&forceMobileApp=0&language=PT_BR&forceMobileLayout=0&awsEmail=leoaalvs%40gmail.com", "visit_count": 0}, {"date_added": "0", "date_last_used": "0", "guid": "7866c967-e96c-44ae-908b-4ea3b2e97b88", "id": "120", "name": "AWS Servers - Google Drive", "show_icon": false, "source": "sync", "type": "url", "url": "https://drive.google.com/drive/folders/19LCjpyomizqUl5CGO8lQsLkHiFtRjMQf", "visit_count": 0}, {"date_added": "0", "date_last_used": "0", "guid": "f3a291c1-1f8b-47f6-99b5-de1499969871", "id": "121", "name": "www.udemy.com", "show_icon": false, "source": "sync", "type": "url", "url": "https://www.udemy.com/rest-apis-development-with-loopback/learn/lecture/9442174#overview", "visit_count": 0}, {"date_added": "0", "date_last_used": "0", "guid": "bbc004d3-0de2-4f57-bb41-e20256f69aa3", "id": "122", "name": "www.udemy.com", "show_icon": false, "source": "sync", "type": "url", "url": "https://www.udemy.com/courses/search/?q=sails%20js&src=sac&kw=sails", "visit_count": 0}, {"date_added": "0", "date_last_used": "0", "guid": "b9242a27-afde-4490-88d5-49e8eb0191c4", "id": "123", "name": "LoopBack API Explorer", "show_icon": false, "source": "sync", "type": "url", "url": "http://3.220.20.62:3000/explorer/#!/Message/Message_greet", "visit_count": 0}, {"date_added": "0", "date_last_used": "0", "guid": "4d1dd53f-0e7e-401b-afcd-b6342fb44123", "id": "124", "name": "Search results for: 'loopback'", "show_icon": false, "source": "sync", "type": "url", "url": "https://www.packtpub.com/catalogsearch/result/?q=loopback", "visit_count": 0}, {"date_added": "0", "date_last_used": "0", "guid": "5c13904d-04cf-4663-8815-4553a260bb5c", "id": "125", "name": "loopback - <PERSON><PERSON><PERSON>", "show_icon": false, "source": "sync", "type": "url", "url": "https://www.lynda.com/search?q=loopback", "visit_count": 0}, {"date_added": "0", "date_last_used": "0", "guid": "586dd1bd-1fe5-48bf-8745-528a6c273976", "id": "126", "name": "Building and running ML models for data scientists", "show_icon": false, "source": "sync", "type": "url", "url": "https://www.lynda.com/Data-Science-tutorials/Building-running-ML-models-data-scientists/711823/775356-4.html?srchtrk=index%3a30%0alinktypeid%3a2%0aq%3adocker%0apage%3a1%0as%3arelevance%0asa%3atrue%0aproducttypeid%3a2", "visit_count": 0}, {"date_added": "0", "date_last_used": "0", "guid": "f3a48d45-66e6-4ca5-9d4e-293c1b02026e", "id": "127", "name": "mesg.ebay.com", "show_icon": false, "source": "sync", "type": "url", "url": "https://mesg.ebay.com/mesgweb/ViewMessageDetail/0/All/107793309993", "visit_count": 0}, {"date_added": "0", "date_last_used": "0", "guid": "d63dc5ce-1aaa-42c9-9186-1acd01176df8", "id": "128", "name": "mysql - <PERSON><PERSON>", "show_icon": false, "source": "sync", "type": "url", "url": "https://hub.docker.com/_/mysql", "visit_count": 0}, {"date_added": "0", "date_last_used": "0", "guid": "277ee33f-b086-49f1-b8e4-454d08f72b20", "id": "129", "name": "mail.google.com", "show_icon": false, "source": "sync", "type": "url", "url": "https://mail.google.com/mail/u/0/#inbox/********************************", "visit_count": 0}, {"date_added": "0", "date_last_used": "0", "guid": "de95a5f6-7881-465b-a6e5-56e6a4188171", "id": "130", "name": "phpmyadmin/phpmyadmin - <PERSON><PERSON>", "show_icon": false, "source": "sync", "type": "url", "url": "https://hub.docker.com/r/phpmyadmin/phpmyadmin/", "visit_count": 0}, {"date_added": "0", "date_last_used": "0", "guid": "62f4105b-daac-4756-9392-f72ded4a09e5", "id": "131", "name": "Implementing auto-migration | LoopBack Documentation", "show_icon": false, "source": "sync", "type": "url", "url": "https://loopback.io/doc/en/lb3/Implementing-auto-migration.html", "visit_count": 0}, {"date_added": "0", "date_last_used": "0", "guid": "c76eda7b-ffe7-4f36-8269-685aae67f635", "id": "132", "name": "3.220.20.62:3000", "show_icon": false, "source": "sync", "type": "url", "url": "http://3.220.20.62:3000/", "visit_count": 0}, {"date_added": "0", "date_last_used": "0", "guid": "a46ef1c4-5c8d-4514-b2de-78788b56f1b1", "id": "133", "name": "Orange Pi PC ArcadeRetro1.1 imagem 32GB RetrOrangepi 4.2 Full + Kodi + GPIO", "show_icon": false, "source": "sync", "type": "url", "url": "http://www.orangepiretro.tk/2019/01/orange-pi-pc-arcaderetro11-imagem-32gb.html", "visit_count": 0}, {"date_added": "0", "date_last_used": "0", "guid": "fa0d7e98-35fa-4df5-b5c9-7ba2c865949b", "id": "134", "name": "Connecting to MySQL Database using NodeJS", "show_icon": false, "source": "sync", "type": "url", "url": "https://o7planning.org/en/11959/connecting-to-mysql-database-using-nodejs", "visit_count": 0}, {"date_added": "0", "date_last_used": "0", "guid": "d3454c67-6700-48ec-90e0-2681a2c73cc0", "id": "135", "name": "tutum/mongodb Tags - <PERSON><PERSON>", "show_icon": false, "source": "sync", "type": "url", "url": "https://hub.docker.com/r/tutum/mongodb/tags", "visit_count": 0}, {"date_added": "0", "date_last_used": "0", "guid": "ade11f2f-2958-4f10-8a49-ba75faf39456", "id": "136", "name": "Docker: <PERSON><PERSON><PERSON> servid<PERSON> - dockerbr - Medium", "show_icon": false, "source": "sync", "type": "url", "url": "https://medium.com/dockerbr/mongodb-no-docker-dd3b72c7efb7", "visit_count": 0}, {"date_added": "0", "date_last_used": "0", "guid": "012bc2da-3902-49e6-88ef-83102cae87c4", "id": "137", "name": "mongod 68f2b48c5ffb", "show_icon": false, "source": "sync", "type": "url", "url": "http://3.220.20.62:28017/", "visit_count": 0}, {"date_added": "0", "date_last_used": "0", "guid": "4543a522-9187-4e05-b544-7552d7ea5a6c", "id": "138", "name": "www.udemy.com", "show_icon": false, "source": "sync", "type": "url", "url": "https://www.udemy.com/courses/search/?q=loopback%20nodejs&src=sac&kw=loopback&p=2", "visit_count": 0}, {"date_added": "0", "date_last_used": "0", "guid": "b0d7b79a-15af-44e3-b52f-942880a7ef6e", "id": "139", "name": "www.udemy.com", "show_icon": false, "source": "sync", "type": "url", "url": "https://www.udemy.com/unity-rest-api-node-js-banco-de-dados-online/", "visit_count": 0}, {"date_added": "0", "date_last_used": "0", "guid": "ccd89c43-5dd2-4332-8a77-7a068682f7b2", "id": "140", "name": "14 Best NodeJS Frameworks for Developers in 2019", "show_icon": false, "source": "sync", "type": "url", "url": "https://www.tecmint.com/best-nodejs-frameworks-for-developers/", "visit_count": 0}, {"date_added": "0", "date_last_used": "0", "guid": "22de329a-520c-4dfa-826f-17c81b348233", "id": "141", "name": "web.whatsapp.com", "show_icon": false, "source": "sync", "type": "url", "url": "https://web.whatsapp.com/", "visit_count": 0}, {"date_added": "0", "date_last_used": "0", "guid": "0e933f3d-b0c0-4498-bfbf-75a41e63a2d7", "id": "142", "name": "www.youtube.com", "show_icon": false, "source": "sync", "type": "url", "url": "https://www.youtube.com/watch?v=UwBj3Xw7wmw&list=PLYO9T4zW_XsnN9dqUm76GY-Hr2TfS7Z8h&index=5", "visit_count": 0}, {"date_added": "0", "date_last_used": "0", "guid": "78531a7d-e922-4ea0-a1b4-5a141e61f0e4", "id": "143", "name": "3.220.20.62", "show_icon": false, "source": "sync", "type": "url", "url": "http://3.220.20.62:3000/", "visit_count": 0}, {"date_added": "0", "date_last_used": "0", "guid": "b97ed76b-e5a1-4256-96c8-9c1eda02a114", "id": "144", "name": "(271) How to Use a GUI with Ubuntu Linux on AWS EC2 - YouTube", "show_icon": false, "source": "sync", "type": "url", "url": "https://www.youtube.com/watch?v=6x_okhl_CF4", "visit_count": 0}, {"date_added": "0", "date_last_used": "0", "guid": "5089bab8-87eb-4b5e-9f0d-f2b0dbd10fa8", "id": "145", "name": "Best FREE Unity Assets - Over 200 Quality Curated Assets for Unity 3D", "show_icon": false, "source": "sync", "type": "url", "url": "http://www.procedural-worlds.com/blog/best-free-unity-assets-categorised-mega-list/", "visit_count": 0}], "date_added": "13294812331215817", "date_last_used": "0", "date_modified": "13294812331215817", "guid": "3d859d88-467b-4ab3-a897-d0198fef557e", "id": "118", "name": "temporary Panic", "source": "unknown", "type": "folder"}], "date_added": "13294812331215103", "date_last_used": "0", "date_modified": "13294812331215103", "guid": "1b021947-b872-419b-8ec0-37c3de9b5243", "id": "90", "name": "Other Bookmarks", "source": "unknown", "type": "folder"}, {"children": [{"date_added": "0", "date_last_used": "0", "guid": "2d51ae57-fbe4-4f4e-b9f1-31ef04353f15", "id": "147", "name": "The Pirate Filmes - O primeiro em BluRay compactado no Brasil", "show_icon": false, "source": "sync", "type": "url", "url": "http://www.thepiratefilmes.com/", "visit_count": 0}], "date_added": "13294812331216690", "date_last_used": "0", "date_modified": "13294812331216690", "guid": "7d198472-3d14-4ac2-a419-1dd6c8b4eea1", "id": "146", "name": "Synced Bookmarks", "source": "unknown", "type": "folder"}], "date_added": "13400377491287752", "date_last_used": "0", "date_modified": "13231010011000000", "guid": "82b081ec-3dd3-529c-8475-ab6c344590dd", "id": "2", "name": "Outros favoritos", "source": "unknown", "type": "folder"}, "synced": {"children": [{"date_added": "13027528238369000", "date_last_used": "0", "guid": "ab651368-a73f-4077-b852-ba8fc5ef003d", "id": "6", "name": "The Pirate Filmes - O primeiro em BluRay compactado no Brasil", "show_icon": false, "source": "sync", "type": "url", "url": "http://www.thepiratefilmes.com/", "visit_count": 0}], "date_added": "13400377491287752", "date_last_used": "0", "date_modified": "13027528238369000", "guid": "4cf2e351-0e85-532b-bb37-df045d8f8d0f", "id": "3", "name": "Favoritos do celular", "source": "unknown", "type": "folder"}}, "sync_metadata": "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", "version": 1}